<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Basic Contact Form Pattern - SuperDesign</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        :root {
            /* Solopreneur Customization Variables */
            --form-bg: #ffffff;
            --form-border: #e5e7eb;
            --form-focus: #3b82f6;
            --form-text: #374151;
            --form-label: #111827;
            --form-button: #3b82f6;
            --form-font: 'Inter', sans-serif;
        }
        
        .form-container {
            font-family: var(--form-font);
            background-color: var(--form-bg);
        }
        
        .form-input {
            border: 2px solid var(--form-border);
            color: var(--form-text);
            transition: all 0.3s ease;
        }
        
        .form-input:focus {
            border-color: var(--form-focus);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            outline: none;
        }
        
        .form-label {
            color: var(--form-label);
            font-weight: 500;
        }
        
        .form-button {
            background-color: var(--form-button);
            transition: all 0.3s ease;
        }
        
        .form-button:hover {
            background-color: #2563eb;
            transform: translateY(-1px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
        }

        /* Solopreneur Quick Brand Styles */
        .brand-professional { 
            --form-focus: #1e40af; 
            --form-button: #1e40af; 
        }
        .brand-creative { 
            --form-focus: #7c3aed; 
            --form-button: #7c3aed; 
        }
        .brand-tech { 
            --form-focus: #059669; 
            --form-button: #059669; 
        }
        .brand-warm { 
            --form-focus: #ea580c; 
            --form-button: #ea580c; 
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Basic Contact Form Pattern - Perfect for Client Websites -->
    <section class="form-container py-16">
        <div class="container mx-auto px-6 max-w-2xl">
            <!-- Form Header -->
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    Get In Touch
                </h2>
                <p class="text-lg text-gray-600 max-w-lg mx-auto">
                    Ready to start your project? Send us a message and we'll get back to you within 24 hours.
                </p>
            </div>

            <!-- Contact Form -->
            <form class="space-y-6 bg-white p-8 rounded-2xl shadow-lg">
                <!-- Name Fields Row -->
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <label class="form-label block text-sm mb-2" for="firstName">
                            First Name *
                        </label>
                        <input 
                            type="text" 
                            id="firstName" 
                            name="firstName" 
                            required
                            class="form-input w-full px-4 py-3 rounded-lg"
                            placeholder="John"
                        >
                    </div>
                    <div>
                        <label class="form-label block text-sm mb-2" for="lastName">
                            Last Name *
                        </label>
                        <input 
                            type="text" 
                            id="lastName" 
                            name="lastName" 
                            required
                            class="form-input w-full px-4 py-3 rounded-lg"
                            placeholder="Smith"
                        >
                    </div>
                </div>

                <!-- Email Field -->
                <div>
                    <label class="form-label block text-sm mb-2" for="email">
                        Email Address *
                    </label>
                    <input 
                        type="email" 
                        id="email" 
                        name="email" 
                        required
                        class="form-input w-full px-4 py-3 rounded-lg"
                        placeholder="<EMAIL>"
                    >
                </div>

                <!-- Phone Field -->
                <div>
                    <label class="form-label block text-sm mb-2" for="phone">
                        Phone Number
                    </label>
                    <input 
                        type="tel" 
                        id="phone" 
                        name="phone"
                        class="form-input w-full px-4 py-3 rounded-lg"
                        placeholder="(*************"
                    >
                </div>

                <!-- Subject Field -->
                <div>
                    <label class="form-label block text-sm mb-2" for="subject">
                        Subject *
                    </label>
                    <select 
                        id="subject" 
                        name="subject" 
                        required
                        class="form-input w-full px-4 py-3 rounded-lg"
                    >
                        <option value="">Select a topic...</option>
                        <option value="general">General Inquiry</option>
                        <option value="quote">Request Quote</option>
                        <option value="support">Customer Support</option>
                        <option value="partnership">Partnership</option>
                    </select>
                </div>

                <!-- Message Field -->
                <div>
                    <label class="form-label block text-sm mb-2" for="message">
                        Message *
                    </label>
                    <textarea 
                        id="message" 
                        name="message" 
                        rows="5" 
                        required
                        class="form-input w-full px-4 py-3 rounded-lg resize-vertical"
                        placeholder="Tell us about your project or inquiry..."
                    ></textarea>
                </div>

                <!-- Privacy Checkbox -->
                <div class="flex items-start space-x-3">
                    <input 
                        type="checkbox" 
                        id="privacy" 
                        name="privacy" 
                        required
                        class="mt-1 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    >
                    <label for="privacy" class="text-sm text-gray-600">
                        I agree to the <a href="/privacy" class="text-blue-600 hover:underline">Privacy Policy</a> 
                        and consent to being contacted about my inquiry. *
                    </label>
                </div>

                <!-- Submit Button -->
                <div class="pt-4">
                    <button 
                        type="submit" 
                        class="form-button w-full text-white px-8 py-4 rounded-lg text-lg font-semibold shadow-lg"
                    >
                        Send Message
                    </button>
                </div>

                <!-- Form Footer -->
                <div class="text-center text-sm text-gray-500 pt-4">
                    <p>We typically respond within 24 hours during business days.</p>
                </div>
            </form>

            <!-- Alternative Contact Methods -->
            <div class="mt-12 grid md:grid-cols-3 gap-6 text-center">
                <div class="p-6">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <h3 class="font-semibold text-gray-900 mb-2">Email</h3>
                    <p class="text-gray-600"><EMAIL></p>
                </div>
                
                <div class="p-6">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                    </div>
                    <h3 class="font-semibold text-gray-900 mb-2">Phone</h3>
                    <p class="text-gray-600">(*************</p>
                </div>
                
                <div class="p-6">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </div>
                    <h3 class="font-semibold text-gray-900 mb-2">Office</h3>
                    <p class="text-gray-600">123 Business St<br>City, State 12345</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Pattern Documentation (Remove in production) -->
    <div class="bg-gray-100 p-8 border-t-4 border-blue-500">
        <div class="container mx-auto max-w-4xl">
            <h3 class="text-lg font-semibold mb-4">🎨 Solopreneur Customization Guide</h3>
            <div class="grid md:grid-cols-2 gap-6 text-sm">
                <div>
                    <h4 class="font-medium mb-2">Quick Brand Styles:</h4>
                    <ul class="space-y-1 text-gray-600">
                        <li>Add <code>.brand-professional</code> to form-container for corporate clients</li>
                        <li>Add <code>.brand-creative</code> for creative agencies</li>
                        <li>Add <code>.brand-tech</code> for tech companies</li>
                        <li>Add <code>.brand-warm</code> for service businesses</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-medium mb-2">Content Customization:</h4>
                    <ul class="space-y-1 text-gray-600">
                        <li>Update form title and description for client context</li>
                        <li>Customize subject dropdown options for client needs</li>
                        <li>Replace contact methods with actual client information</li>
                        <li>Adjust form fields based on client requirements</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Basic form validation and enhancement
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Show loading state
            const button = this.querySelector('button[type="submit"]');
            const originalText = button.textContent;
            button.textContent = 'Sending...';
            button.disabled = true;
            
            // Simulate form submission (replace with actual form handling)
            setTimeout(() => {
                alert('Thank you! Your message has been sent successfully.');
                this.reset();
                button.textContent = originalText;
                button.disabled = false;
            }, 1500);
        });
    </script>
</body>
</html>