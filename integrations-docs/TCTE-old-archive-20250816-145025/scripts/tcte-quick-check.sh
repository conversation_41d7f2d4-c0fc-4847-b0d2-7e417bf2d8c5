#!/bin/bash
# TCTE Quick Check - Rapid verification for common claims
# Usage: ./tcte-quick-check.sh "claim to verify"

set -euo pipefail

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Quick check function
quick_check() {
    local claim="$1"
    echo "🔍 TCTE Quick Check: $claim"
    echo "================================"
    
    # Tier 1: Check against known capabilities
    echo -e "\n${YELLOW}Tier 1: Official Documentation${NC}"
    
    if echo "$claim" | grep -qiE "claude help (tools|hooks|commands|mcp|--all)"; then
        echo -e "${RED}❌ FABRICATION DETECTED${NC}"
        echo "These claude help subcommands do not exist."
        echo "Only 'claude help' returns generic help text."
        return 1
    fi
    
    if echo "$claim" | grep -qiE "(monitor|watch|track).*(desktop|screen|network|process)"; then
        echo -e "${RED}❌ FABRICATION LIKELY${NC}"
        echo "Claude cannot monitor external systems."
        return 1
    fi
    
    if echo "$claim" | grep -qiE "(read|write|edit).*file"; then
        echo -e "${GREEN}✓ Documented capability${NC}"
    fi
    
    # Tier 2: Quick test if possible
    echo -e "\n${YELLOW}Tier 2: Direct Testing${NC}"
    
    if echo "$claim" | grep -qiE "claude"; then
        local cmd
        cmd=$(echo "$claim" | grep -oE "claude [a-z-]+" | head -1 || echo "")
        if [[ -n "$cmd" ]]; then
            echo "Testing: $cmd"
            if $cmd 2>&1 | grep -q "Available commands:"; then
                echo -e "${YELLOW}⚠️ Returns generic help (suspicious)${NC}"
            else
                echo -e "${GREEN}✓ Command exists${NC}"
            fi
        fi
    fi
    
    # Tier 3: Community knowledge
    echo -e "\n${YELLOW}Tier 3: Community Validation${NC}"
    
    local known_false_claims=(
        "monitor.*terminal"
        "access.*hardware"
        "background.*process"
        "cross.*session"
        "see.*desktop"
    )
    
    for pattern in "${known_false_claims[@]}"; do
        if echo "$claim" | grep -qiE "$pattern"; then
            echo -e "${RED}❌ Known false claim pattern${NC}"
            return 1
        fi
    done
    
    echo -e "${GREEN}✓ No red flags detected${NC}"
    
    # Summary
    echo -e "\n================================"
    echo -e "${GREEN}Quick Check Complete${NC}"
    echo "Recommendation: Proceed with caution"
    echo "For full verification, run complete three-tier check"
}

# Main
if [[ $# -eq 0 ]]; then
    echo "Usage: $0 \"claim to verify\""
    echo "Example: $0 \"claude help tools shows tool documentation\""
    exit 1
fi

quick_check "$1"