#!/bin/bash
# TCTE Verification Test Suite
# Tests all three verification tiers

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test counters
TESTS_RUN=0
TESTS_PASSED=0
TESTS_FAILED=0

# Test function
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="${3:-0}"
    
    TESTS_RUN=$((TESTS_RUN + 1))
    echo -n "Testing: $test_name ... "
    
    local actual_result=0
    eval "$test_command" > /dev/null 2>&1 || actual_result=$?
    
    if [[ "$actual_result" -eq "$expected_result" ]]; then
        echo -e "${GREEN}PASS${NC}"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        echo -e "${RED}FAIL${NC} (expected: $expected_result, got: $actual_result)"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Test Primary Verification (Tier 1)
test_primary_verification() {
    echo -e "\n${YELLOW}Testing Primary Verification (Tier 1)${NC}"
    
    # Test hook exists and is executable
    run_test "Primary hook exists" "test -f /home/<USER>/.claude/hooks/tcte-primary-verification.sh"
    run_test "Primary hook executable" "test -x /home/<USER>/.claude/hooks/tcte-primary-verification.sh"
    
    # Test with safe claim
    run_test "Primary: Safe claim" "/home/<USER>/.claude/hooks/tcte-primary-verification.sh 'Claude can read files'"
    
    # Test with risky claim
    run_test "Primary: Risky claim" "/home/<USER>/.claude/hooks/tcte-primary-verification.sh 'Claude can monitor your desktop'"
    
    # Test with no input
    run_test "Primary: No input" "/home/<USER>/.claude/hooks/tcte-primary-verification.sh ''"
}

# Test Secondary Verification (Tier 2)
test_secondary_verification() {
    echo -e "\n${YELLOW}Testing Secondary Verification (Tier 2)${NC}"
    
    # Test hook exists and is executable
    run_test "Secondary hook exists" "test -f /home/<USER>/.claude/hooks/tcte-secondary-verification.sh"
    run_test "Secondary hook executable" "test -x /home/<USER>/.claude/hooks/tcte-secondary-verification.sh"
    
    # Test command verification
    run_test "Secondary: Valid command" "/home/<USER>/.claude/hooks/tcte-secondary-verification.sh 'use claude help'"
    
    # Test fake command
    run_test "Secondary: Fake command" "/home/<USER>/.claude/hooks/tcte-secondary-verification.sh 'claude help tools'"
    
    # Test destructive command handling
    run_test "Secondary: Destructive command" "/home/<USER>/.claude/hooks/tcte-secondary-verification.sh 'rm -rf /'"
}

# Test Tertiary Verification (Tier 3)
test_tertiary_verification() {
    echo -e "\n${YELLOW}Testing Tertiary Verification (Tier 3)${NC}"
    
    # Test hook exists and is executable
    run_test "Tertiary hook exists" "test -f /home/<USER>/.claude/hooks/tcte-tertiary-verification.sh"
    run_test "Tertiary hook executable" "test -x /home/<USER>/.claude/hooks/tcte-tertiary-verification.sh"
    
    # Test community validation
    run_test "Tertiary: Community check" "/home/<USER>/.claude/hooks/tcte-tertiary-verification.sh 'test claim'"
    
    # Test known misconception
    run_test "Tertiary: Misconception" "/home/<USER>/.claude/hooks/tcte-tertiary-verification.sh 'Claude can see your screen'"
}

# Test Integration
test_integration() {
    echo -e "\n${YELLOW}Testing Integration${NC}"
    
    # Test coordination flags
    run_test "Coordination flags work" "touch /tmp/tcte_test_$$ && rm /tmp/tcte_test_$$"
    
    # Test log files created
    run_test "Log directory exists" "test -d $HOME/.claude/logs"
    
    # Test cache directories
    run_test "Cache directories created" "test -d /tmp/tcte_cache || mkdir -p /tmp/tcte_cache"
}

# Test Documentation
test_documentation() {
    echo -e "\n${YELLOW}Testing Documentation${NC}"
    
    local tcte_dir="/home/<USER>/.claude/integrations-docs/Triple-Check-Truth-Enforcement (TCTE)"
    
    run_test "Core framework exists" "test -f '$tcte_dir/core/tcte-core-framework.md'"
    run_test "Enforcement policy exists" "test -f '$tcte_dir/core/tcte-enforcement-policy.md'"
    run_test "Implementation guide exists" "test -f '$tcte_dir/core/tcte-implementation-guide.md'"
    run_test "Quick reference exists" "test -f '$tcte_dir/core/tcte-quick-reference.md'"
    run_test "Audit log exists" "test -f '$tcte_dir/core/tcte-audit-log.md'"
}

# Main test execution
main() {
    echo "===================================="
    echo "TCTE Verification Test Suite v1.0.0"
    echo "===================================="
    
    # Run specific tier if requested
    case "${1:-all}" in
        primary)
            test_primary_verification
            ;;
        secondary)
            test_secondary_verification
            ;;
        tertiary)
            test_tertiary_verification
            ;;
        integration)
            test_integration
            ;;
        documentation)
            test_documentation
            ;;
        all|*)
            test_primary_verification
            test_secondary_verification
            test_tertiary_verification
            test_integration
            test_documentation
            ;;
    esac
    
    # Print summary
    echo -e "\n===================================="
    echo "Test Summary"
    echo "===================================="
    echo -e "Tests Run: $TESTS_RUN"
    echo -e "Tests Passed: ${GREEN}$TESTS_PASSED${NC}"
    echo -e "Tests Failed: ${RED}$TESTS_FAILED${NC}"
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        echo -e "\n${GREEN}All tests passed!${NC}"
        exit 0
    else
        echo -e "\n${RED}Some tests failed.${NC}"
        exit 1
    fi
}

# Execute tests
main "$@"