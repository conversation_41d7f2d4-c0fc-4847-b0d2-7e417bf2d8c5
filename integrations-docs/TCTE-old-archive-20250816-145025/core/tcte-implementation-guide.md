# TCTE Implementation Guide
*Version: 1.0.0*
*Status: ACTIVE*
*Last Updated: 2025-08-12*

## Quick Start

### Prerequisites
- Access to Claude Code CLI
- Bash shell environment
- Git repository (optional but recommended)
- Internet access for community validation

### Basic Setup
```bash
# 1. Navigate to TCTE directory
cd "/home/<USER>/.claude/integrations-docs/Triple-Check-Truth-Enforcement (TCTE)"

# 2. Verify installation
ls -la core/ hooks/ scripts/ tests/

# 3. Run initial test
./scripts/tcte-quick-check.sh

# 4. Check hook integration
grep -l "tcte" ~/.claude/settings.json
```

## Step-by-Step Implementation

### Step 1: Enable TCTE Hooks

#### Add to settings.json
```json
{
  "userPromptSubmitHooks": [
    "/home/<USER>/.claude/hooks/tcte-primary-verification.sh",
    "/home/<USER>/.claude/hooks/tcte-secondary-verification.sh",
    "/home/<USER>/.claude/hooks/tcte-tertiary-verification.sh"
  ],
  "tcteVerification": {
    "enabled": true,
    "autoVerify": true,
    "strictMode": true,
    "logLevel": "info"
  }
}
```

### Step 2: Configure Verification Levels

#### Set Verification Thresholds
```bash
# In tcte-config.sh
export TCTE_PRIMARY_THRESHOLD=0.8    # 80% confidence required
export TCTE_SECONDARY_THRESHOLD=0.9  # 90% test pass rate
export TCTE_TERTIARY_THRESHOLD=0.7   # 70% community validation
export TCTE_OVERALL_THRESHOLD=0.85   # 85% combined confidence
```

### Step 3: Initialize Audit System

#### Create Audit Structure
```bash
# Initialize audit files
touch audit/verification-log.jsonl
touch audit/fabrication-registry.md
echo '{"version":"1.0.0","metrics":{}}' > audit/performance-metrics.json

# Set permissions
chmod 644 audit/*.{jsonl,md,json}
```

### Step 4: Test the System

#### Run Verification Tests
```bash
# Test individual tiers
./tests/test-tcte-verification.sh --tier primary
./tests/test-tcte-verification.sh --tier secondary
./tests/test-tcte-verification.sh --tier tertiary

# Test enforcement
./tests/test-tcte-enforcement.sh

# Full system test
./tests/run-all-tests.sh
```

## Integration Points

### CLAUDE.md Integration

#### Add Principle 7
```markdown
Principle 7: **TRIPLE CHECK TRUTH ENFORCEMENT** - All capability claims MUST be verified through three-tier validation (Official Docs, Direct Testing, Community Validation) before acceptance
```

### Hook Integration

#### UserPromptSubmit Hooks
1. **Primary Hook**: Checks claims against official documentation
2. **Secondary Hook**: Triggers direct testing when needed
3. **Tertiary Hook**: Validates with community sources

#### Hook Coordination
```bash
# Coordination flags prevent duplicate checks
/tmp/tcte_verified_[session_id]
/tmp/tcte_testing_[claim_hash]
/tmp/tcte_community_[claim_hash]
```

### Compliance Integration

#### Update compliance-expert.md
```markdown
## Triple Check Verification Capability
- Three-tier evidence-based validation
- Automated fabrication detection
- Real-time claim verification
- Comprehensive audit trail
```

## Configuration Options

### Basic Configuration
```bash
# Enable/disable TCTE
TCTE_ENABLED=true

# Set verification mode
TCTE_MODE="strict"  # strict, normal, permissive

# Configure logging
TCTE_LOG_LEVEL="info"  # debug, info, warn, error
TCTE_LOG_FILE="/home/<USER>/.claude/logs/tcte.log"
```

### Advanced Configuration
```bash
# Caching
TCTE_CACHE_ENABLED=true
TCTE_CACHE_TTL=3600  # 1 hour

# Parallel verification
TCTE_PARALLEL=true
TCTE_MAX_WORKERS=3

# Community sources
TCTE_GITHUB_ENABLED=true
TCTE_DISCORD_ENABLED=false
TCTE_STACKOVERFLOW_ENABLED=true
```

## Verification Workflows

### For New Claims
1. Identify claim in user prompt or response
2. Check cache for recent verification
3. Run three-tier verification
4. Log results to audit trail
5. Apply enforcement policy

### For Existing Features
1. Schedule periodic re-verification
2. Compare with previous results
3. Identify changes or drift
4. Update documentation
5. Notify of discrepancies

### For Documentation Updates
1. Extract all claims from document
2. Batch verify claims
3. Mark verified/unverified sections
4. Generate verification report
5. Update document with results

## Troubleshooting

### Common Issues

#### Hooks Not Triggering
```bash
# Check hook permissions
ls -la ~/.claude/hooks/tcte-*.sh

# Verify settings.json
jq '.userPromptSubmitHooks' ~/.claude/settings.json

# Check hook logs
tail -f ~/.claude/logs/hooks.log
```

#### Verification Failures
```bash
# Check network connectivity
ping -c 1 github.com

# Verify tool permissions
claude /doctor

# Review error logs
grep ERROR ~/.claude/logs/tcte.log
```

#### Performance Issues
```bash
# Check cache status
du -sh ~/.claude/integrations-docs/Triple-Check-Truth-Enforcement\ \(TCTE\)/cache/

# Monitor verification time
time ./scripts/tcte-verify-claim.sh "test claim"

# Adjust parallel workers
export TCTE_MAX_WORKERS=1
```

## Best Practices

### Verification Standards
1. Always complete all three tiers when possible
2. Document evidence sources
3. Prefer recent sources over older ones
4. Weight official documentation highest
5. Consider context and use case

### Documentation
1. Timestamp all verifications
2. Include evidence links
3. Note confidence levels
4. Track changes over time
5. Maintain audit trail

### Performance
1. Use caching for repeated verifications
2. Batch verify related claims
3. Run community checks asynchronously
4. Optimize regex patterns
5. Clean old cache entries

## Maintenance Schedule

### Daily Tasks
- Review new claims queue
- Process urgent verifications
- Update fabrication registry

### Weekly Tasks
- Run verification backlog
- Analyze patterns
- Update cache
- Review metrics

### Monthly Tasks
- Full system audit
- Performance optimization
- Documentation update
- Community feedback review

### Quarterly Tasks
- Framework review
- Success metric analysis
- Tool updates
- Training updates