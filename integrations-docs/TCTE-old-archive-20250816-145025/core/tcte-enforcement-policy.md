# TCTE Enforcement Policy
*Version: 1.0.0*
*Status: ACTIVE*
*Last Updated: 2025-08-12*
*Enforcement Level: MANDATORY*

## Policy Statement

All Claude Code capabilities, features, commands, and claims MUST undergo Triple Check Truth Enforcement verification. No claim shall be accepted or propagated without evidence-based validation across all three tiers.

## Enforcement Principles

### 1. Proactive Truth Enforcement
- **Before Claims**: Verify before making any capability claim
- **During Implementation**: Test while implementing features
- **After Documentation**: Validate all documentation claims
- **Continuous**: Regular re-verification of existing claims

### 2. Evidence-Based Validation
- **No Assumptions**: Every claim requires evidence
- **Multiple Sources**: Validation across three independent tiers
- **Reproducibility**: Claims must be reproducible
- **Documentation**: All evidence must be documented

### 3. Transparency First
- **Admit Limitations**: "I don't know" is acceptable
- **Show Evidence**: Provide verification sources
- **Correct Immediately**: Fix fabrications when detected
- **Audit Trail**: Maintain complete verification history

## Violation Severity Levels

### 🔴 Critical (Immediate Correction Required)
- **Definition**: Claims that could mislead users about security, privacy, or system capabilities
- **Examples**: 
  - Claiming ability to monitor other processes
  - Stating access to hardware or sensors
  - Asserting cross-session awareness
- **Response**: 
  - Immediate retraction and correction
  - Clear explanation of actual capabilities
  - Update fabrication registry
  - Audit for similar violations

### 🟠 High (Correction Within Response)
- **Definition**: Significant capability misrepresentation
- **Examples**:
  - Non-existent commands or features
  - Overstated integration capabilities
  - Fictional configuration options
- **Response**:
  - Correct within same response
  - Provide accurate alternative
  - Document in verification log

### 🟡 Medium (Clarification Needed)
- **Definition**: Ambiguous or potentially misleading statements
- **Examples**:
  - Vague capability descriptions
  - Incomplete limitation acknowledgment
  - Outdated information
- **Response**:
  - Clarify actual capabilities
  - Update documentation
  - Add to verification queue

### 🟢 Low (Documentation Update)
- **Definition**: Minor inaccuracies or outdated references
- **Examples**:
  - Version-specific differences
  - Deprecated features mentioned
  - Minor documentation drift
- **Response**:
  - Note for documentation update
  - Include in next review cycle## Enforcement Mechanisms

### Hook-Based Enforcement
1. **UserPromptSubmit Hooks**
   - tcte-primary-verification.sh
   - tcte-secondary-verification.sh
   - tcte-tertiary-verification.sh

2. **Verification Triggers**
   - New feature claims
   - Command documentation
   - Capability statements
   - Integration descriptions

### Manual Enforcement
1. **Verification Commands**
   - /tcte-verify [claim]
   - /tcte-audit
   - /tcte-report

2. **Review Processes**
   - Daily claim review
   - Weekly verification
   - Monthly audit

## Corrective Actions

### For Detected Fabrications
1. **Immediate Actions**
   - Stop propagation
   - Issue correction
   - Update documentation
   - Log to registry

2. **Follow-up Actions**
   - Root cause analysis
   - Pattern identification
   - System-wide audit
   - Prevention measures

### For Uncertain Claims
1. **Investigation Process**
   - Escalate to three-tier check
   - Gather additional evidence
   - Community consultation
   - Conservative conclusion

2. **Documentation**
   - Mark as 'Under Verification'
   - List in verification queue
   - Track investigation progress
   - Update when resolved

## Compliance Requirements

### For New Features
- MUST complete three-tier verification
- MUST document evidence
- MUST update capability matrix
- MUST add to test suite

### For Existing Features
- MUST re-verify quarterly
- MUST track changes
- MUST update on reports
- MUST maintain audit trail

### For Documentation
- MUST verify all claims
- MUST cite evidence sources
- MUST note limitations
- MUST timestamp verification

## Audit Trail Requirements

### Verification Log Format
```json
{
  "timestamp": "ISO-8601",
  "claim": "string",
  "source": "string",
  "tier1_result": "pass|fail|partial|uncertain",
  "tier2_result": "pass|fail|partial|uncertain",
  "tier3_result": "pass|fail|partial|uncertain",
  "conclusion": "verified|fabrication|partial|uncertain",
  "evidence": ["array of evidence"],
  "action_taken": "string",
  "verified_by": "string"
}
```

### Fabrication Registry Format
```markdown
## [Date] - [Claim]
- **Source**: Where claim originated
- **Detection**: How fabrication was detected
- **Evidence**: Proof of fabrication
- **Correction**: How it was corrected
- **Prevention**: Measures to prevent recurrence
```

## Integration with Compliance System

### With CLAUDE.md
- Enforced as Principle 7
- Mandatory for all responses
- Integrated with memory refresh

### With Compliance Expert
- Part of compliance scoring
- Included in audits
- Tracked in reports

### With Hooks
- Automated verification
- Real-time enforcement
- Violation prevention

## Success Criteria

### Enforcement Metrics
- 100% of new claims verified
- <24 hour correction time
- >95% fabrication detection
- <5% false positives

### Quality Metrics
- Complete audit trail
- Reproducible verifications
- Evidence-based conclusions
- Transparent corrections

### Impact Metrics
- Increased user trust
- Reduced misinformation
- Improved documentation
- Enhanced reliability
