# TCTE Core Framework
*Version: 1.0.0*
*Status: ACTIVE*
*Last Updated: 2025-08-12*

## Overview

The Triple Check Truth Enforcement (TCTE) framework is a three-tier verification system that ensures all Claude Code capabilities, features, and claims are validated through evidence-based verification.

## The Three Tiers of Verification

### 🥇 Tier 1: Primary Verification - System Cards & Official Documentation

#### Sources
- **System Cards**: Claude 3, 3.7, Opus 4, Opus 4.1 model cards
- **Official Docs**: https://docs.anthropic.com/en/docs/claude-code
- **Release Notes**: Version-specific capabilities and limitations
- **API Documentation**: Technical specifications and constraints

#### Verification Process
1. Check official Claude model capabilities
2. Review documented limitations and known issues
3. Validate against official command references
4. Cross-reference with API specifications

#### Key Indicators
- ✅ Explicitly documented capability
- ⚠️ Partially documented or ambiguous
- ❌ Not mentioned in official sources

### 🥈 Tier 2: Secondary Verification - Direct Testing & Experimentation

#### Testing Protocol
1. **Command Testing**: Execute the exact command/feature
2. **Behavior Analysis**: Observe actual vs expected behavior
3. **Error Analysis**: Examine error messages for insights
4. **Edge Case Testing**: Push boundaries of claimed capabilities

#### Test Categories
- **Existence Tests**: Does the feature/command exist?
- **Functionality Tests**: Does it work as claimed?
- **Performance Tests**: Does it meet stated performance?
- **Integration Tests**: Does it integrate as documented?

#### Documentation Requirements
```bash
# Test template
echo "Testing: [FEATURE_NAME]"
echo "Expected: [EXPECTED_BEHAVIOR]"
[TEST_COMMAND] 2>&1 | tee test-output.txt
echo "Actual: [OBSERVED_BEHAVIOR]"
echo "Verdict: [PASS/FAIL/PARTIAL]"
```

### 🥉 Tier 3: Tertiary Verification - Community & External Validation

#### Community Sources
- GitHub Issues and Discussions
- Discord and community forums
- Stack Overflow questions
- User experience reports
- Blog posts and tutorials

#### External Validation
- Independent testing results
- Third-party documentation
- Academic research papers
- Security audits
- Performance benchmarks

#### Synthesis Process
1. Gather community reports
2. Identify common patterns
3. Validate against tiers 1 & 2
4. Draw consensus conclusions

## Verification Workflow

### Step 1: Claim Identification
- Document the specific claim
- Note source and context
- Identify testable components

### Step 2: Three-Tier Check
1. **Primary**: Check official sources
2. **Secondary**: Test directly
3. **Tertiary**: Validate externally

### Step 3: Evidence Synthesis
- Compare all three tiers
- Identify discrepancies
- Weight evidence appropriately

### Step 4: Conclusion
- **Verified**: All tiers confirm
- **Partial**: Mixed evidence
- **Fabrication**: Evidence contradicts claim
- **Uncertain**: Insufficient evidence

## Red Flags for Fabrication

### 🚩 High-Risk Indicators
- Returns generic help instead of specific documentation
- Feature exists only in documentation, not practice
- Elaborate descriptions with no actual functionality
- Capabilities that contradict official limitations
- Commands that simplify to basic operations

### 🟡 Medium-Risk Indicators
- Ambiguous or vague documentation
- Inconsistent behavior across tests
- Limited community validation
- Recent features with sparse documentation

### 🟢 Low-Risk Indicators
- Well-documented in official sources
- Consistent test results
- Strong community validation
- Long-standing stable features

## Integration Points

### With CLAUDE.md
- Principle 7: Triple Check Truth Enforcement
- Replaces pattern-based no-lying policy
- Provides evidence-based validation

### With Hooks
- `tcte-primary-verification.sh`: Tier 1 checks
- `tcte-secondary-verification.sh`: Tier 2 testing
- `tcte-tertiary-verification.sh`: Tier 3 validation

### With Compliance System
- Integrated into compliance-expert.md
- Part of comprehensive update checklist
- Audit trail in verification log

## Success Metrics

### Coverage Metrics
- Percentage of claims verified
- Percentage with all three tiers
- Time to complete verification

### Quality Metrics
- Fabrication detection rate
- False positive rate
- Evidence quality score
- Reproducibility rate

### Impact Metrics
- Trust improvement
- Error reduction
- User satisfaction
- Documentation accuracy

## Maintenance

### Daily
- Log new claims for verification
- Update verification queue

### Weekly
- Process verification backlog
- Update fabrication registry

### Monthly
- Analyze verification patterns
- Update red flag indicators
- Review success metrics

### Quarterly
- Framework effectiveness review
- Update verification procedures
- Community feedback integration