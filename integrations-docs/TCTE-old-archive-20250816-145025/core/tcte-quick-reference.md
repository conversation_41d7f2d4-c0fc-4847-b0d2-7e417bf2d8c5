# TCTE Quick Reference
*One-page verification guide*
*Print and keep handy*

## 🚀 Rapid Verification Checklist

### Before Making Any Claim
- [ ] Check official Claude Code docs
- [ ] Search model card limitations
- [ ] Test the feature directly
- [ ] Look for error messages
- [ ] Check GitHub issues
- [ ] Search community forums
- [ ] Compare all evidence
- [ ] Draw conservative conclusion

## 🚩 Fabrication Red Flags

### STOP if you see:
- **Generic help response** → Feature doesn't exist
- **No error when expected** → Functionality missing
- **Too good to be true** → Likely fabrication
- **Contradicts official docs** → Definitely false
- **No one else can reproduce** → Probably fictional

## ⚡ Quick Command Tests

### Test if command exists
```bash
claude help [command] 2>&1 | grep -v "Available commands"
# If output is generic help → Command is fake
```

### Test if feature works
```bash
[feature_command] 2>&1 | tee test.log
# Check if actual behavior matches claim
```

### Check official docs
```bash
curl -s https://docs.anthropic.com/en/docs/claude-code | grep -i "[feature]"
```

## 📊 Verification Decision Tree

```
Claim Made
    ↓
Official Docs?
    ├─ YES → Test It
    │    ├─ Works → Community Check
    │    │    ├─ Confirmed → ✅ VERIFIED
    │    │    └─ Mixed → ⚠️ PARTIAL
    │    └─ Fails → ❌ FABRICATION
    └─ NO → Test Anyway
         ├─ Works → Deep Investigation
         └─ Fails → ❌ LIKELY FABRICATION
```

## 🎯 Severity Quick Guide

| Level | Action | Example |
|-------|--------|---------|
| 🔴 Critical | Stop & Correct NOW | "I can access your camera" |
| 🟠 High | Fix in response | "claude help tools exists" |
| 🟡 Medium | Clarify | "might be able to..." |
| 🟢 Low | Note for update | "v1.2 feature" (outdated) |

## 🔍 Evidence Weighting

1. **Official Docs** (40%) - Highest weight
2. **Direct Testing** (35%) - Second highest
3. **Community** (25%) - Supporting evidence

**Minimum for Verification**: 2 out of 3 tiers must confirm

## 💨 Speed Verification Commands

### One-liner claim check
```bash
./scripts/tcte-quick-check.sh "claim text here"
```

### Batch verify document
```bash
./scripts/tcte-verify-claims.sh document.md
```

### Generate audit report
```bash
./scripts/tcte-audit-report.sh --today
```

## 🏃 Emergency Procedures

### If you made a false claim:
1. **STOP** - Don't compound the error
2. **CORRECT** - "I need to correct that..."
3. **EXPLAIN** - Give accurate information
4. **DOCUMENT** - Log to fabrication registry
5. **PREVENT** - Update patterns/checks

### If unsure about claim:
1. **ACKNOWLEDGE** - "I'm not certain..."
2. **INVESTIGATE** - Run three-tier check
3. **CONSERVATIVE** - Err on side of caution
4. **TRANSPARENT** - Show verification process

## 📝 Audit Log Template

```json
{
  "timestamp": "2025-08-12T10:30:00Z",
  "claim": "Feature X exists",
  "tier1": "not_found",
  "tier2": "failed", 
  "tier3": "no_mentions",
  "verdict": "fabrication",
  "action": "corrected"
}
```

## 🔗 Key Resources

- **Official Docs**: https://docs.anthropic.com/en/docs/claude-code
- **GitHub Issues**: https://github.com/anthropics/claude-code/issues
- **Model Cards**: Check Anthropic's model documentation
- **This Guide**: ~/integrations-docs/Triple-Check-Truth-Enforcement (TCTE)/

## ⚠️ Remember

**When in doubt → Three-tier check**
**No evidence → Don't claim**
**Found fabrication → Correct immediately**
**Trust but verify → Everything**

---
*TCTE v1.0.0 | Print date: Use `date` command | Keep updated*