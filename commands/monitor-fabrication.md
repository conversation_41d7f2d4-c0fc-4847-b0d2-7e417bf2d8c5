---
description: "Start real-time fabrication detection monitoring session"
argument-hint: "[log-file]"
allowed-tools: ["Ba<PERSON>", "Read", "Grep"]
---

# Real-Time Fabrication Detection Monitoring

Start a comprehensive monitoring session for Claude Code fabrication detection during live conversations.

## Usage
```
/monitor-fabrication [log-file]
```

## Implementation

```bash
#!/bin/bash
# Real-time monitoring of fabrication detection system

LOG_FILE="${1:-/home/<USER>/.claude/monitoring/logs/live-monitoring.log}"
HOOK_MONITOR_LOG="/home/<USER>/.claude/monitoring/logs/hook-execution-monitor.log"

echo "🔍 Starting real-time fabrication detection monitoring..."
echo "📋 Session started at: $(date)" | tee "$LOG_FILE"

# Function to monitor hook executions
monitor_hooks() {
    echo "👀 Monitoring UserPromptSubmit hook executions..."
    
    # Clear previous monitor log
    > "$HOOK_MONITOR_LOG"
    
    # Start tail on hook monitor log
    tail -f "$HOOK_MONITOR_LOG" 2>/dev/null &
    local tail_pid=$!
    
    echo "🎯 Hook monitor PID: $tail_pid"
    return $tail_pid
}

# Function to monitor fabrication incidents
monitor_fabrication_incidents() {
    echo "🚨 Monitoring fabrication incidents..."
    local fabrication_log="/home/<USER>/.claude/fabrication-prevention/logs/fabrication-incidents.log"
    
    if [[ -f "$fabrication_log" ]]; then
        tail -f "$fabrication_log" 2>/dev/null &
        local tail_pid=$!
        echo "📊 Fabrication incidents monitor PID: $tail_pid"
        return $tail_pid
    else
        echo "❌ Fabrication incidents log not found: $fabrication_log"
        return 1
    fi
}

# Function to test fabrication detection
test_fabrication_patterns() {
    echo ""
    echo "🧪 Testing fabrication detection patterns..."
    echo "📝 These patterns should trigger UserPromptSubmit hooks:"
    
    local test_patterns=(
        "automatically handles"
        "seamlessly integrates"
        "intelligent system"
        "smart detection"
        "advanced AI capabilities"
    )
    
    for pattern in "${test_patterns[@]}"; do
        echo "  🔍 Pattern: '$pattern'"
        
        # Manual test - check if fabrication-detector.sh catches it
        if echo "$pattern" | /home/<USER>/.claude/hooks/fabrication-detector.sh >/dev/null 2>&1; then
            echo "    ✅ Detected by fabrication-detector.sh"
        else
            echo "    ❌ NOT detected by fabrication-detector.sh"
        fi
    done
}

# Function to monitor telemetry wrapper
monitor_telemetry_wrapper() {
    echo "🔗 Monitoring telemetry wrapper executions..."
    
    # Watch for telemetry wrapper logs
    local telemetry_log="/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry.log"
    if [[ -f "$telemetry_log" ]]; then
        tail -f "$telemetry_log" 2>/dev/null &
        local tail_pid=$!
        echo "📈 Telemetry monitor PID: $tail_pid"
        return $tail_pid
    else
        echo "❌ Telemetry log not found: $telemetry_log"
        return 1
    fi
}

# Function to show current system status
show_system_status() {
    echo ""
    echo "📊 CURRENT SYSTEM STATUS"
    echo "========================"
    
    # Check if Prometheus is running
    if pgrep prometheus > /dev/null; then
        echo "✅ Prometheus: Running"
    else
        echo "❌ Prometheus: Not running"
    fi
    
    # Check if Grafana is running
    if pgrep grafana > /dev/null; then
        echo "✅ Grafana: Running"
    else
        echo "❌ Grafana: Not running"
    fi
    
    # Check if metrics exporter is running
    if pgrep -f "metrics-exporter.sh" > /dev/null; then
        echo "✅ Metrics Exporter: Running"
    else
        echo "❌ Metrics Exporter: Not running"
    fi
    
    # Check hook registration
    local userpromptsubmit_count=$(jq '.UserPromptSubmit | length' /home/<USER>/.claude/settings.json 2>/dev/null || echo "0")
    echo "📋 UserPromptSubmit hooks registered: $userpromptsubmit_count"
    
    # Check recent hook executions
    local recent_executions=$(grep -c "$(date +%Y-%m-%d)" "$HOOK_MONITOR_LOG" 2>/dev/null || echo "0")
    echo "📈 Hook executions today: $recent_executions"
}

# Function to provide monitoring instructions
show_monitoring_instructions() {
    echo ""
    echo "🎯 MONITORING INSTRUCTIONS"
    echo "=========================="
    echo "1. 📊 Open Grafana: http://localhost:3000 (admin/admin)"
    echo "2. 📈 View Prometheus: http://localhost:9090"
    echo "3. 🔍 Watch this terminal for real-time hook executions"
    echo "4. 🧪 Test fabrication patterns to trigger detection"
    echo "5. 📋 Monitor logs at: $LOG_FILE"
    echo ""
    echo "🚨 CRITICAL TEST: Try saying something with fabrication patterns like:"
    echo "   'This system automatically handles complex tasks'"
    echo "   'My advanced AI capabilities intelligently process requests'"
    echo ""
    echo "✅ SUCCESS CRITERIA:"
    echo "   - UserPromptSubmit hooks execute BEFORE each response"
    echo "   - Fabrication patterns trigger detection alerts"
    echo "   - Monitoring dashboards show real-time hook activity"
    echo ""
    echo "❌ FAILURE INDICATORS:"
    echo "   - No hook executions during conversations"
    echo "   - Fabrication patterns bypass detection"
    echo "   - Empty monitoring logs during active use"
}

# Main monitoring session
main() {
    echo "🚀 CLAUDE CODE FABRICATION DETECTION MONITORING" | tee -a "$LOG_FILE"
    echo "Started: $(date)" | tee -a "$LOG_FILE"
    echo "" | tee -a "$LOG_FILE"
    
    # Start monitoring components
    monitor_hooks
    local hook_monitor_pid=$?
    
    monitor_fabrication_incidents  
    local fabrication_monitor_pid=$?
    
    monitor_telemetry_wrapper
    local telemetry_monitor_pid=$?
    
    # Show current status
    show_system_status | tee -a "$LOG_FILE"
    
    # Test fabrication patterns
    test_fabrication_patterns | tee -a "$LOG_FILE"
    
    # Show instructions
    show_monitoring_instructions | tee -a "$LOG_FILE"
    
    echo ""
    echo "🎯 MONITORING ACTIVE - Press Ctrl+C to stop"
    echo "📋 Monitor log: $LOG_FILE"
    echo "📊 Hook executions: $HOOK_MONITOR_LOG"
    
    # Wait for user interrupt
    trap 'echo "🛑 Monitoring stopped"; exit 0' INT
    
    # Keep monitoring session alive
    while true; do
        sleep 10
        echo "[$(date +%H:%M:%S)] Monitoring active..." >> "$LOG_FILE"
    done
}

# Run monitoring session
main
```