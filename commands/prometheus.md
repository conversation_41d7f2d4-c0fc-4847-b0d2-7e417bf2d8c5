---
description: "Launch Prometheus monitoring for Claude Code hook execution"
argument-hint: "[config-file]"
allowed-tools: ["Bash", "Read", "Write"]
---

# Prometheus Hook Monitoring Command

Launch Prometheus with pre-configured queries for Claude Code hook execution monitoring.

## Usage
```
/prometheus [config-file]
```

## Implementation

```bash
#!/bin/bash
# Start Prometheus with Claude Code hook monitoring configuration

CONFIG_FILE="${1:-/home/<USER>/.claude/monitoring/prometheus/prometheus.yml}"
METRICS_EXPORTER="/home/<USER>/.claude/monitoring/prometheus/metrics-exporter.sh"

echo "🔍 Starting Prometheus monitoring for Claude Code hooks..."

# Start metrics exporter in background
if ! pgrep -f "metrics-exporter.sh --serve" > /dev/null; then
    echo "📊 Starting metrics exporter on port 9091..."
    nohup "$METRICS_EXPORTER" --serve > /tmp/metrics-exporter.log 2>&1 &
    sleep 2
fi

# Start Prometheus
echo "🚀 Starting Prometheus with config: $CONFIG_FILE"
prometheus --config.file="$CONFIG_FILE" \
           --storage.tsdb.path="/tmp/prometheus-data" \
           --web.console.templates="/usr/share/prometheus/consoles" \
           --web.console.libraries="/usr/share/prometheus/console_libraries" \
           --web.listen-address="0.0.0.0:9090" \
           --log.level=info &

PROMETHEUS_PID=$!
echo "📈 Prometheus started (PID: $PROMETHEUS_PID) - Access at http://localhost:9090"

# Pre-load useful queries
echo "🔍 Useful Prometheus queries for Claude Code hooks:"
echo "  1. claude_hook_executions_total{type=\"UserPromptSubmit\"}"
echo "  2. rate(claude_hook_executions_total[1m])"
echo "  3. claude_fabrication_detection_score"
echo "  4. claude_hook_registration_status"

# Check if hooks are executing
sleep 3
echo "📋 Current hook execution status:"
curl -s "http://localhost:9091/metrics" | grep "claude_hook_executions_total" || echo "❌ No hook executions detected"

wait $PROMETHEUS_PID
```