---
description: "Display current Claude Code hook registration and execution status"
argument-hint: "[detailed]"
allowed-tools: ["Ba<PERSON>", "Read", "Grep", "Glob"]
---

# Hook Status Diagnostic

Display comprehensive information about Claude Code hook registration and execution status.

## Usage
```
/hook-status [detailed]
```

## Implementation

```bash
#!/bin/bash
# Claude Code Hook Status Diagnostic

DETAILED="${1:-false}"
SETTINGS_FILE="/home/<USER>/.claude/settings.json"
HOOKS_DIR="/home/<USER>/.claude/hooks"

echo "🔍 CLAUDE CODE HOOK STATUS DIAGNOSTIC"
echo "====================================="
echo "Timestamp: $(date)"
echo "Settings file: $SETTINGS_FILE"
echo ""

# Function to check hook registration
check_hook_registration() {
    echo "📋 HOOK REGISTRATION STATUS"
    echo "-------------------------"
    
    if [[ ! -f "$SETTINGS_FILE" ]]; then
        echo "❌ ERROR: settings.json not found"
        return 1
    fi
    
    # Parse hook configuration
    local userpromptsubmit_hooks=$(jq -r '.UserPromptSubmit[].hooks[]?.command' "$SETTINGS_FILE" 2>/dev/null | grep -o '[^/]*\.sh' | sort | uniq)
    local stop_hooks=$(jq -r '.Stop[].hooks[]?.command' "$SETTINGS_FILE" 2>/dev/null | grep -o '[^/]*\.sh' | sort | uniq)
    local pretooluse_hooks=$(jq -r '.PreToolUse[].hooks[]?.command' "$SETTINGS_FILE" 2>/dev/null | grep -o '[^/]*\.sh' | sort | uniq)
    local posttooluse_hooks=$(jq -r '.PostToolUse[].hooks[]?.command' "$SETTINGS_FILE" 2>/dev/null | grep -o '[^/]*\.sh' | sort | uniq)
    
    echo "🎯 UserPromptSubmit hooks:"
    if [[ -n "$userpromptsubmit_hooks" ]]; then
        echo "$userpromptsubmit_hooks" | while read -r hook; do
            if [[ -f "$HOOKS_DIR/$hook" ]]; then
                echo "  ✅ $hook (file exists)"
            else
                echo "  ❌ $hook (file missing)"
            fi
        done
    else
        echo "  ❌ No UserPromptSubmit hooks found"
    fi
    
    echo ""
    echo "🛑 Stop hooks:"
    if [[ -n "$stop_hooks" ]]; then
        echo "$stop_hooks" | while read -r hook; do
            if [[ -f "$HOOKS_DIR/$hook" ]]; then
                echo "  ✅ $hook (file exists)"
            else
                echo "  ❌ $hook (file missing)"
            fi
        done
    else
        echo "  ❌ No Stop hooks found"
    fi
    
    echo ""
    echo "⚡ PreToolUse hooks:"
    if [[ -n "$pretooluse_hooks" ]]; then
        echo "$pretooluse_hooks" | while read -r hook; do
            if [[ -f "$HOOKS_DIR/$hook" ]]; then
                echo "  ✅ $hook (file exists)"
            else
                echo "  ❌ $hook (file missing)"
            fi
        done
    else
        echo "  ❌ No PreToolUse hooks found"
    fi
    
    echo ""
    echo "📤 PostToolUse hooks:"
    if [[ -n "$posttooluse_hooks" ]]; then
        echo "$posttooluse_hooks" | while read -r hook; do
            if [[ -f "$HOOKS_DIR/$hook" ]]; then
                echo "  ✅ $hook (file exists)"
            else
                echo "  ❌ $hook (file missing)"
            fi
        done
    else
        echo "  ❌ No PostToolUse hooks found"
    fi
}

# Function to check recent hook executions
check_hook_executions() {
    echo ""
    echo "📈 RECENT HOOK EXECUTIONS"
    echo "------------------------"
    
    local hook_monitor_log="/home/<USER>/.claude/monitoring/logs/hook-execution-monitor.log"
    local fabrication_log="/home/<USER>/.claude/fabrication-prevention/logs/fabrication-incidents.log"
    local memory_log="/home/<USER>/.claude/hooks/logs/memory-enforcement.log"
    
    # Check hook execution monitor
    if [[ -f "$hook_monitor_log" ]]; then
        local monitor_executions=$(wc -l < "$hook_monitor_log" 2>/dev/null || echo "0")
        local recent_monitor=$(grep "$(date +%Y-%m-%d)" "$hook_monitor_log" 2>/dev/null | wc -l || echo "0")
        echo "🎯 Hook execution monitor: $monitor_executions total, $recent_monitor today"
        
        if [[ "$DETAILED" == "true" ]] && [[ $recent_monitor -gt 0 ]]; then
            echo "   Recent executions:"
            grep "$(date +%Y-%m-%d)" "$hook_monitor_log" | tail -5 | sed 's/^/     /'
        fi
    else
        echo "❌ Hook execution monitor log not found"
    fi
    
    # Check fabrication incidents
    if [[ -f "$fabrication_log" ]]; then
        local fabrication_incidents=$(wc -l < "$fabrication_log" 2>/dev/null || echo "0")
        local recent_incidents=$(grep "$(date +%Y-%m-%d)" "$fabrication_log" 2>/dev/null | wc -l || echo "0")
        echo "🚨 Fabrication incidents: $fabrication_incidents total, $recent_incidents today"
        
        if [[ "$DETAILED" == "true" ]] && [[ $recent_incidents -gt 0 ]]; then
            echo "   Recent incidents:"
            grep "$(date +%Y-%m-%d)" "$fabrication_log" | tail -3 | sed 's/^/     /'
        fi
    else
        echo "❌ Fabrication incidents log not found"
    fi
    
    # Check memory enforcement log
    if [[ -f "$memory_log" ]]; then
        local memory_size=$(wc -l < "$memory_log" 2>/dev/null || echo "0")
        local recent_memory=$(grep "$(date +%Y-%m-%d)" "$memory_log" 2>/dev/null | wc -l || echo "0")
        echo "🧠 Memory enforcement: $memory_size total, $recent_memory today"
    else
        echo "❌ Memory enforcement log not found"
    fi
}

# Function to check fabrication detection system
check_fabrication_detection() {
    echo ""
    echo "🔍 FABRICATION DETECTION SYSTEM"
    echo "------------------------------"
    
    local detector_script="/home/<USER>/.claude/hooks/fabrication-detector.sh"
    local recovery_script="/home/<USER>/.claude/hooks/fabrication-recovery-loop.sh"
    local team_member_script="/home/<USER>/.claude/hooks/valued-team-member.sh"
    
    # Check fabrication detector
    if [[ -f "$detector_script" ]]; then
        if [[ -x "$detector_script" ]]; then
            echo "✅ fabrication-detector.sh: Exists and executable"
            
            # Test with a fabrication pattern
            if echo "automatically handles" | "$detector_script" >/dev/null 2>&1; then
                echo "   ✅ Pattern detection: Working"
            else
                echo "   ❌ Pattern detection: Failed"
            fi
        else
            echo "❌ fabrication-detector.sh: Not executable"
        fi
    else
        echo "❌ fabrication-detector.sh: Not found"
    fi
    
    # Check recovery loop
    if [[ -f "$recovery_script" ]]; then
        if [[ -x "$recovery_script" ]]; then
            echo "✅ fabrication-recovery-loop.sh: Exists and executable"
        else
            echo "❌ fabrication-recovery-loop.sh: Not executable"
        fi
    else
        echo "❌ fabrication-recovery-loop.sh: Not found"
    fi
    
    # Check valued team member
    if [[ -f "$team_member_script" ]]; then
        if [[ -x "$team_member_script" ]]; then
            echo "✅ valued-team-member.sh: Exists and executable"
        else
            echo "❌ valued-team-member.sh: Not executable"
        fi
    else
        echo "❌ valued-team-member.sh: Not found"
    fi
}

# Function to check telemetry wrapper
check_telemetry_wrapper() {
    echo ""
    echo "🔗 TELEMETRY WRAPPER STATUS"
    echo "-------------------------"
    
    local wrapper_script="/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh"
    
    if [[ -f "$wrapper_script" ]]; then
        if [[ -x "$wrapper_script" ]]; then
            echo "✅ telemetry-wrapper.sh: Exists and executable"
            
            # Check if all hooks are wrapped
            local wrapped_hooks=$(grep -c "telemetry-wrapper.sh" "$SETTINGS_FILE" 2>/dev/null || echo "0")
            echo "📊 Hooks using telemetry wrapper: $wrapped_hooks"
            
            # This could be the source of the UserPromptSubmit hook execution failure
            echo "⚠️  CRITICAL: All hooks are wrapped - potential JSON input parsing issue"
            
        else
            echo "❌ telemetry-wrapper.sh: Not executable"
        fi
    else
        echo "❌ telemetry-wrapper.sh: Not found"
    fi
}

# Function to check monitoring infrastructure
check_monitoring_infrastructure() {
    echo ""
    echo "📊 MONITORING INFRASTRUCTURE"
    echo "---------------------------"
    
    # Check Prometheus
    if pgrep prometheus > /dev/null; then
        echo "✅ Prometheus: Running (PID: $(pgrep prometheus))"
    else
        echo "❌ Prometheus: Not running"
    fi
    
    # Check Grafana
    if pgrep grafana > /dev/null; then
        echo "✅ Grafana: Running (PID: $(pgrep grafana))"
    else
        echo "❌ Grafana: Not running"
    fi
    
    # Check metrics exporter
    if pgrep -f "metrics-exporter.sh" > /dev/null; then
        echo "✅ Metrics Exporter: Running (PID: $(pgrep -f "metrics-exporter.sh"))"
    else
        echo "❌ Metrics Exporter: Not running"
    fi
    
    # Check monitoring ports
    echo ""
    echo "🌐 Monitoring ports:"
    if netstat -tlnp 2>/dev/null | grep -q ":9090"; then
        echo "  ✅ Port 9090 (Prometheus): Open"
    else
        echo "  ❌ Port 9090 (Prometheus): Closed"
    fi
    
    if netstat -tlnp 2>/dev/null | grep -q ":3000"; then
        echo "  ✅ Port 3000 (Grafana): Open"
    else
        echo "  ❌ Port 3000 (Grafana): Closed"
    fi
    
    if netstat -tlnp 2>/dev/null | grep -q ":9091"; then
        echo "  ✅ Port 9091 (Metrics): Open"
    else
        echo "  ❌ Port 9091 (Metrics): Closed"
    fi
}

# Function to provide diagnostic recommendations
provide_recommendations() {
    echo ""
    echo "💡 DIAGNOSTIC RECOMMENDATIONS"
    echo "=============================="
    
    # Check for common issues
    if ! jq empty "$SETTINGS_FILE" 2>/dev/null; then
        echo "🚨 CRITICAL: settings.json has invalid JSON syntax"
    fi
    
    if ! grep -q "fabrication-detector.sh" "$SETTINGS_FILE" 2>/dev/null; then
        echo "🚨 CRITICAL: fabrication-detector.sh not registered in UserPromptSubmit hooks"
    fi
    
    if grep -q "telemetry-wrapper.sh" "$SETTINGS_FILE" 2>/dev/null; then
        echo "⚠️  WARNING: All hooks use telemetry wrapper - potential JSON input parsing issue"
        echo "   → Recommendation: Test if telemetry wrapper interferes with hook execution"
    fi
    
    local hook_monitor_log="/home/<USER>/.claude/monitoring/logs/hook-execution-monitor.log"
    if [[ ! -f "$hook_monitor_log" ]] || [[ ! -s "$hook_monitor_log" ]]; then
        echo "🚨 CRITICAL: No hook executions logged - UserPromptSubmit hooks may not be running"
        echo "   → Recommendation: Start /monitor-fabrication to test live hook execution"
    fi
    
    if ! pgrep prometheus > /dev/null; then
        echo "💡 TIP: Start monitoring with /prometheus command"
    fi
    
    if ! pgrep grafana > /dev/null; then
        echo "💡 TIP: Start dashboard with /grafana-hooks command"
    fi
}

# Main execution
main() {
    check_hook_registration
    check_hook_executions
    check_fabrication_detection
    check_telemetry_wrapper
    check_monitoring_infrastructure
    provide_recommendations
    
    echo ""
    echo "✅ Hook status diagnostic complete"
    echo "📋 For live monitoring, run: /monitor-fabrication"
    echo "📊 For dashboard access, run: /grafana-hooks"
}

# Run diagnostic
main
```