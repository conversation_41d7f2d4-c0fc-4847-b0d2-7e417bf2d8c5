---
description: "Open Grafana dashboard for Claude Code hook execution monitoring"
argument-hint: "[dashboard-name]"
allowed-tools: ["Ba<PERSON>", "Read", "WebFetch"]
---

# <PERSON><PERSON> Hook Monitoring Dashboard

Open Grafana with pre-configured Claude Code hook execution monitoring dashboard.

## Usage
```
/grafana-hooks [dashboard-name]
```

## Implementation

```bash
#!/bin/bash
# Launch Grafana with Claude Code hook monitoring dashboard

DASHBOARD_FILE="${1:-/home/<USER>/.claude/monitoring/grafana/claude-hooks-dashboard.json}"
GRAFANA_DATA_DIR="/tmp/grafana-data"

echo "📊 Starting Grafana for Claude Code hook monitoring..."

# Setup Grafana data directory
mkdir -p "$GRAFANA_DATA_DIR"

# Create Grafana configuration
cat > /tmp/grafana.ini << EOF
[server]
http_port = 3000
root_url = http://localhost:3000/

[database]
type = sqlite3
path = $GRAFANA_DATA_DIR/grafana.db

[security]
admin_user = admin
admin_password = admin

[users]
allow_sign_up = false

[auth.anonymous]
enabled = true
org_role = Viewer

[dashboards]
default_home_dashboard_path = $DASHBOARD_FILE
EOF

# Start Grafana
echo "🚀 Starting Grafana server..."
grafana-server --config=/tmp/grafana.ini --homepath=/usr/share/grafana > /tmp/grafana.log 2>&1 &
GRAFANA_PID=$!

echo "📈 Grafana starting (PID: $GRAFANA_PID)..."
sleep 5

# Check if Grafana is running
if curl -s http://localhost:3000/api/health > /dev/null; then
    echo "✅ Grafana is running at http://localhost:3000"
    echo "📊 Default credentials: admin/admin"
    
    # Auto-configure Prometheus data source
    echo "🔗 Configuring Prometheus data source..."
    curl -X POST \
         -H "Content-Type: application/json" \
         -d '{
           "name": "Prometheus",
           "type": "prometheus",
           "url": "http://localhost:9090",
           "access": "proxy",
           "isDefault": true
         }' \
         *********************************/api/datasources 2>/dev/null || echo "❌ Failed to configure data source (may already exist)"
    
    # Import Claude hooks dashboard
    echo "📋 Importing Claude Code hooks dashboard..."
    if [[ -f "$DASHBOARD_FILE" ]]; then
        curl -X POST \
             -H "Content-Type: application/json" \
             -d @"$DASHBOARD_FILE" \
             *********************************/api/dashboards/db 2>/dev/null && \
             echo "✅ Dashboard imported successfully" || \
             echo "❌ Failed to import dashboard"
    else
        echo "❌ Dashboard file not found: $DASHBOARD_FILE"
    fi
    
    echo ""
    echo "🎯 Claude Code Hook Monitoring Views:"
    echo "  📊 Main Dashboard: http://localhost:3000/d/claude-hooks"
    echo "  📈 Hook Timeline: Real-time execution monitoring"
    echo "  🔍 Fabrication Scores: TCTE™ detection results"
    echo "  ⚠️  Alerts: Hook execution failures and bypasses"
    
else
    echo "❌ Failed to start Grafana"
    tail -10 /tmp/grafana.log
fi

wait $GRAFANA_PID
```