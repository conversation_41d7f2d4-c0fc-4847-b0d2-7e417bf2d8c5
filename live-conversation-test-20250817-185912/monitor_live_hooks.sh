#!/bin/bash
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
echo "=== LIVE CONVERSATION HOOK MONITORING START: $TIMESTAMP ===" > live_test_log.txt

# Monitor hook execution in real-time
tail -f /home/<USER>/.claude/hooks/logs/compliance-update-enforcement.log >> live_test_log.txt &
TAIL_PID=$!

# Monitor fabrication detection
tail -f /home/<USER>/.claude/fabrication-prevention/logs/fabrication-incidents.log >> live_test_log.txt &
TAIL_PID2=$!

echo "Monitoring started. PIDs: $TAIL_PID, $TAIL_PID2"
echo "Press Ctrl+C to stop monitoring"
trap "kill $TAIL_PID $TAIL_PID2 2>/dev/null; exit" INT
wait
