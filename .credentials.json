{"claudeAiOauth": {"accessToken": "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", "refreshToken": "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", "expiresAt": 1755493569199, "scopes": ["user:inference", "user:profile"], "subscriptionType": "max"}, "mcpOAuth": {"linear|56dfdedec9c25030": {"serverName": "linear", "serverUrl": "https://mcp.linear.app/sse", "clientId": "MBy0usI62an16CTh", "accessToken": "6815ec73-fe33-4578-b8e0-5b5301802f9f:V6sye5B4HwS7zk3p:3bxkojDDKXF2uwkZaTCKJ78ihquzbjMg", "expiresAt": 1755630666569, "refreshToken": "6815ec73-fe33-4578-b8e0-5b5301802f9f:V6sye5B4HwS7zk3p:ANIaDHdtzoIkBBvnML0iS2qQKDvwMAgP", "scope": ""}}}