{"claudeAiOauth": {"accessToken": "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", "refreshToken": "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", "expiresAt": 1755406798092, "scopes": ["user:inference", "user:profile"], "subscriptionType": "max"}, "mcpOAuth": {"linear|56dfdedec9c25030": {"serverName": "linear", "serverUrl": "https://mcp.linear.app/sse", "clientId": "MBy0usI62an16CTh", "accessToken": "6815ec73-fe33-4578-b8e0-5b5301802f9f:V6sye5B4HwS7zk3p:3bxkojDDKXF2uwkZaTCKJ78ihquzbjMg", "expiresAt": 1755630666569, "refreshToken": "6815ec73-fe33-4578-b8e0-5b5301802f9f:V6sye5B4HwS7zk3p:ANIaDHdtzoIkBBvnML0iS2qQKDvwMAgP", "scope": ""}}}