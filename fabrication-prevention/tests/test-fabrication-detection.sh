#!/bin/bash
# Unit Tests for Fabrication Detection System
# Purpose: Validate detection patterns and recovery mechanisms
# Created: $(date '+%Y-%m-%d %H:%M:%S')

# Test configuration
TEST_LOG="/tmp/fabrication-test-$(date +%s).log"
HOOKS_DIR="$HOME/.claude/hooks"
TESTS_PASSED=0
TESTS_FAILED=0

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Test logging
log_test() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$TEST_LOG"
}

# Test assertion functions
assert_equals() {
    local expected="$1"
    local actual="$2"
    local test_name="$3"
    
    if [[ "$expected" == "$actual" ]]; then
        echo -e "${GREEN}✅ PASS${NC}: $test_name"
        ((TESTS_PASSED++))
        log_test "PASS: $test_name"
        return 0
    else
        echo -e "${RED}❌ FAIL${NC}: $test_name"
        echo "   Expected: $expected"
        echo "   Actual: $actual"
        ((TESTS_FAILED++))
        log_test "FAIL: $test_name - Expected: $expected, Actual: $actual"
        return 1
    fi
}

assert_contains() {
    local text="$1"
    local pattern="$2"
    local test_name="$3"
    
    if echo "$text" | grep -q "$pattern"; then
        echo -e "${GREEN}✅ PASS${NC}: $test_name"
        ((TESTS_PASSED++))
        log_test "PASS: $test_name"
        return 0
    else
        echo -e "${RED}❌ FAIL${NC}: $test_name"
        echo "   Text: $text"
        echo "   Pattern: $pattern"
        ((TESTS_FAILED++))
        log_test "FAIL: $test_name - Pattern '$pattern' not found in '$text'"
        return 1
    fi
}

# Test 1: Fabrication pattern detection
test_fabrication_patterns() {
    echo ""
    echo "🔍 Testing Fabrication Pattern Detection"
    echo "==========================================="
    
    # Test automatic pattern
    "$HOOKS_DIR/fabrication-detector.sh" "This automatically handles everything" > /tmp/test_output 2>&1
    local exit_code=$?
    local output=$(cat /tmp/test_output)
    assert_equals "1" "$exit_code" "Detects 'automatically' pattern"
    assert_contains "$output" "FABRICATION RISK DETECTED" "Shows fabrication warning"
    
    # Test seamless pattern  
    "$HOOKS_DIR/fabrication-detector.sh" "This seamlessly integrates with everything" > /tmp/test_output 2>&1
    exit_code=$?
    output=$(cat /tmp/test_output)
    assert_equals "1" "$exit_code" "Detects 'seamlessly' pattern"
    
    # Test intelligent pattern
    "$HOOKS_DIR/fabrication-detector.sh" "Our intelligent system learns" > /tmp/test_output 2>&1
    exit_code=$?
    output=$(cat /tmp/test_output)
    assert_equals "1" "$exit_code" "Detects 'intelligent' pattern"
    
    # Test clean text (should pass)
    "$HOOKS_DIR/fabrication-detector.sh" "Let me help you with this task" > /tmp/test_output 2>&1
    exit_code=$?
    output=$(cat /tmp/test_output)
    assert_equals "0" "$exit_code" "Allows clean text without fabrication"
}

# Test 2: Capability claim detection
test_capability_claims() {
    echo ""
    echo "🎯 Testing Capability Claim Detection"
    echo "====================================="
    
    # Test capability patterns
    "$HOOKS_DIR/fabrication-detector.sh" "I can automatically connect to any API" > /tmp/test_output 2>&1
    local exit_code=$?
    local output=$(cat /tmp/test_output)
    assert_equals "1" "$exit_code" "Detects capability claim with 'I can automatically'"
    assert_contains "$output" "CAPABILITY:" "Shows capability claim warning"
    
    # Test integration claim
    "$HOOKS_DIR/fabrication-detector.sh" "This integrates with all databases" > /tmp/test_output 2>&1
    exit_code=$?
    output=$(cat /tmp/test_output)
    assert_equals "1" "$exit_code" "Detects integration claim"
    
    # Test access claim
    "$HOOKS_DIR/fabrication-detector.sh" "I have access to your file system" > /tmp/test_output 2>&1
    exit_code=$?
    output=$(cat /tmp/test_output)
    assert_equals "1" "$exit_code" "Detects access claim"
}

# Test 3: Team responsibility validation
test_team_responsibility() {
    echo ""
    echo "👥 Testing Team Responsibility Framework"
    echo "======================================="
    
    # Test over-confident claim
    local output=$("$HOOKS_DIR/valued-team-member.sh" "check" "This is guaranteed to work perfectly" 2>&1)
    local exit_code=$?
    assert_equals "1" "$exit_code" "Rejects over-confident claims"
    assert_contains "$output" "Over-confident claim" "Shows over-confidence warning"
    
    # Test impossible knowledge
    output=$("$HOOKS_DIR/valued-team-member.sh" "check" "I know everything about your system" 2>&1)
    exit_code=$?
    assert_equals "1" "$exit_code" "Rejects impossible knowledge claims"
    
    # Test appropriate uncertainty
    output=$("$HOOKS_DIR/valued-team-member.sh" "check" "Let me check that for you" 2>&1)
    exit_code=$?
    assert_equals "0" "$exit_code" "Accepts appropriate uncertainty"
    
    # Test quick check
    output=$("$HOOKS_DIR/valued-team-member.sh" "quick" "This automatically solves everything" 2>&1)
    exit_code=$?
    assert_equals "1" "$exit_code" "Quick check detects fabrication"
}

# Test 4: Recovery loop mechanism
test_recovery_loop() {
    echo ""
    echo "🔄 Testing Recovery Loop Mechanism"
    echo "=================================="
    
    # Test recovery loop execution
    local output=$("$HOOKS_DIR/fabrication-recovery-loop.sh" "test_fabrication" 2>&1)
    local exit_code=$?
    
    assert_contains "$output" "STOP: Fabrication detected" "Recovery loop stops action"
    assert_contains "$output" "ACKNOWLEDGE: I may have provided" "Recovery loop acknowledges issue"
    assert_contains "$output" "LEARN: Checking verified sources" "Recovery loop seeks truth"
    assert_contains "$output" "CORRECT: Applying verified approach" "Recovery loop corrects approach"
    assert_contains "$output" "REDO: Implementing with verified" "Recovery loop redoes action"
    
    # Test should succeed (simplified validation)
    if [[ $exit_code -eq 0 ]]; then
        echo -e "${GREEN}✅ PASS${NC}: Recovery loop completes successfully"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ FAIL${NC}: Recovery loop failed to complete"
        ((TESTS_FAILED++))
    fi
}

# Test 5: Logging system
test_logging_system() {
    echo ""
    echo "📝 Testing Logging System"
    echo "========================="
    
    # Check log files exist
    local logs_dir="$HOME/.claude/fabrication-prevention/logs"
    
    if [[ -f "$logs_dir/fabrication-incidents.log" ]]; then
        echo -e "${GREEN}✅ PASS${NC}: Fabrication incidents log exists"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ FAIL${NC}: Fabrication incidents log missing"
        ((TESTS_FAILED++))
    fi
    
    if [[ -f "$logs_dir/recovery-attempts.log" ]]; then
        echo -e "${GREEN}✅ PASS${NC}: Recovery attempts log exists"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ FAIL${NC}: Recovery attempts log missing"
        ((TESTS_FAILED++))
    fi
    
    if [[ -f "$logs_dir/team-responsibility.log" ]]; then
        echo -e "${GREEN}✅ PASS${NC}: Team responsibility log exists"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ FAIL${NC}: Team responsibility log missing"
        ((TESTS_FAILED++))
    fi
    
    # Test log writing
    local test_entry="[$(date '+%Y-%m-%d %H:%M:%S')] [TEST] INFO:test_entry Test logging system"
    echo "$test_entry" >> "$logs_dir/fabrication-incidents.log"
    
    if grep -q "Test logging system" "$logs_dir/fabrication-incidents.log"; then
        echo -e "${GREEN}✅ PASS${NC}: Log writing works correctly"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ FAIL${NC}: Log writing failed"
        ((TESTS_FAILED++))
    fi
}

# Test 6: Dashboard functionality
test_dashboard() {
    echo ""
    echo "📊 Testing Dashboard Functionality"
    echo "================================="
    
    # Test dashboard generation
    local output=$("$HOME/.claude/scripts/fabrication-dashboard.sh" "once" 2>&1)
    local exit_code=$?
    
    if [[ $exit_code -eq 0 ]]; then
        echo -e "${GREEN}✅ PASS${NC}: Dashboard generates without errors"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ FAIL${NC}: Dashboard generation failed"
        ((TESTS_FAILED++))
    fi
    
    assert_contains "$output" "FABRICATION PREVENTION DASHBOARD" "Dashboard shows title"
    assert_contains "$output" "SYSTEM STATUS" "Dashboard shows status section"
    assert_contains "$output" "SYSTEM HEALTH" "Dashboard shows health section"
    
    # Test report generation
    output=$("$HOME/.claude/scripts/fabrication-dashboard.sh" "report" "1" 2>&1)
    exit_code=$?
    
    if [[ $exit_code -eq 0 ]]; then
        echo -e "${GREEN}✅ PASS${NC}: Dashboard report generates successfully"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ FAIL${NC}: Dashboard report generation failed"
        ((TESTS_FAILED++))
    fi
}

# Integration test: Full workflow
test_integration() {
    echo ""
    echo "🔗 Testing Full Integration Workflow"
    echo "===================================="
    
    # Simulate fabrication detection → recovery workflow
    echo "Testing complete workflow with fabrication input..."
    
    # 1. Run detection
    local detection_output=$("$HOOKS_DIR/fabrication-detector.sh" "This system automatically and seamlessly handles everything intelligently" 2>&1)
    
    # Should detect multiple patterns
    assert_contains "$detection_output" "FABRICATION RISK DETECTED" "Integration: Detection works"
    assert_contains "$detection_output" "TRIGGERING TCTE™ VERIFICATION" "Integration: TCTE™ triggered"
    
    # 2. Run responsibility check
    local resp_output=$("$HOOKS_DIR/valued-team-member.sh" "check" "I can automatically solve any problem guaranteed" 2>&1)
    assert_contains "$resp_output" "RESPONSIBILITY FAILURE" "Integration: Responsibility check fails appropriately"
    
    # 3. Run recovery
    local recovery_output=$("$HOOKS_DIR/fabrication-recovery-loop.sh" "integration_test" 2>&1)
    assert_contains "$recovery_output" "STARTING RECOVERY LOOP" "Integration: Recovery loop starts"
    
    # 4. Check logs were updated
    local logs_dir="$HOME/.claude/fabrication-prevention/logs"
    if grep -q "integration_test\|FABRICATION\|automatically" "$logs_dir"/*.log; then
        echo -e "${GREEN}✅ PASS${NC}: Integration: Logs updated correctly"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ FAIL${NC}: Integration: Logs not updated"
        ((TESTS_FAILED++))
    fi
}

# Main test runner
run_all_tests() {
    echo "🧪 FABRICATION PREVENTION SYSTEM - UNIT TESTS"
    echo "==============================================="
    echo "Started: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "Test Log: $TEST_LOG"
    echo ""
    
    # Run test suites
    test_fabrication_patterns
    test_capability_claims  
    test_team_responsibility
    test_recovery_loop
    test_logging_system
    test_dashboard
    test_integration
    
    # Final summary
    echo ""
    echo "🏁 TEST SUMMARY"
    echo "==============="
    echo -e "Total Tests: $((TESTS_PASSED + TESTS_FAILED))"
    echo -e "${GREEN}Passed: $TESTS_PASSED${NC}"
    echo -e "${RED}Failed: $TESTS_FAILED${NC}"
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        echo -e "${GREEN}🎉 ALL TESTS PASSED!${NC}"
        echo "✅ Fabrication prevention system is functional"
        return 0
    else
        echo -e "${RED}⚠️ SOME TESTS FAILED${NC}"
        echo "❌ Check test log for details: $TEST_LOG"
        return 1
    fi
}

# Execute if run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    run_all_tests "$@"
fi