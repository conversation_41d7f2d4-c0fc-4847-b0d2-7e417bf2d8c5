# Fabrication Prevention Logging System

## Log Files

### Core Logs
- `fabrication-incidents.log` - Main incident tracking
- `recovery-attempts.log` - Self-correction loop attempts  
- `team-responsibility.log` - Responsibility framework checks

### Log Format
```
[YYYY-MM-DD HH:MM:SS] [COMPONENT] LEVEL:TYPE DETAILS:info
```

### Components
- **FABRICATION**: Pattern detection results
- **RECOVERY**: Self-correction loop progress
- **RESPONSIBILITY**: Team member validation checks
- **TCTE**: TCTE™ verification activities

### Levels
- **INFO**: Normal operations
- **WARNING**: Potential issues
- **HIGH**: Significant fabrication risk
- **CRITICAL**: Requires immediate attention

## Usage

View recent incidents:
```bash
tail -50 fabrication-incidents.log
```

Monitor in real-time:
```bash
tail -f fabrication-incidents.log
```

Generate dashboard:
```bash
~/.claude/scripts/fabrication-dashboard.sh
```