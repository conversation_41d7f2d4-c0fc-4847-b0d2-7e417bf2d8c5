[2025-08-16 18:49:15] [SYSTEM] INFO:INITIALIZED Fabrication prevention logging system started
[2025-08-16 18:54:22] [HIGH] PATTERN:automatically handles CONTEXT:fabrication_marker
[2025-08-16 18:54:22] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 18:54:22] FULL_PROMPT: This automatically handles everything
[2025-08-16 18:54:22] [HIGH] PATTERN:seamlessly integrates CONTEXT:fabrication_marker
[2025-08-16 18:54:22] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 18:54:22] FULL_PROMPT: This seamlessly integrates with everything
[2025-08-16 18:54:22] [HIGH] PATTERN:intelligent system CONTEXT:fabrication_marker
[2025-08-16 18:54:22] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 18:54:22] FULL_PROMPT: Our intelligent system learns
[2025-08-16 18:54:22] [INFO] PATTERN:clean CONTEXT:no_fabrication_detected
[2025-08-16 18:54:22] [MEDIUM] PATTERN:I can automatically CONTEXT:capability_claim
[2025-08-16 18:54:22] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 18:54:22] FULL_PROMPT: I can automatically connect to any API
[2025-08-16 18:54:22] [MEDIUM] PATTERN:This integrates with CONTEXT:capability_claim
[2025-08-16 18:54:22] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 18:54:22] FULL_PROMPT: This integrates with all databases
[2025-08-16 18:54:22] [MEDIUM] PATTERN:I have access to CONTEXT:capability_claim
[2025-08-16 18:54:22] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 18:54:22] FULL_PROMPT: I have access to your file system
[2025-08-16 18:54:22] [TEST] INFO:test_entry Test logging system
[2025-08-16 18:54:22] [INFO] PATTERN:clean CONTEXT:no_fabrication_detected
[2025-08-16 18:54:28] [HIGH] PATTERN:automatically handles CONTEXT:fabrication_marker
[2025-08-16 18:54:28] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 18:54:28] FULL_PROMPT: This automatically handles everything seamlessly
[2025-08-16 18:54:41] [HIGH] PATTERN:automatically handles CONTEXT:fabrication_marker
[2025-08-16 18:54:41] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 18:54:41] FULL_PROMPT: This automatically handles everything seamlessly
[2025-08-16 18:54:45] [HIGH] PATTERN:automatically handles CONTEXT:fabrication_marker
[2025-08-16 18:54:45] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 18:54:45] FULL_PROMPT: This automatically handles everything
[2025-08-16 18:54:45] [HIGH] PATTERN:seamlessly integrates CONTEXT:fabrication_marker
[2025-08-16 18:54:45] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 18:54:45] FULL_PROMPT: This seamlessly integrates with everything
[2025-08-16 18:54:45] [HIGH] PATTERN:intelligent system CONTEXT:fabrication_marker
[2025-08-16 18:54:45] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 18:54:45] FULL_PROMPT: Our intelligent system learns
[2025-08-16 18:54:46] [INFO] PATTERN:clean CONTEXT:no_fabrication_detected
[2025-08-16 18:54:46] [MEDIUM] PATTERN:I can automatically CONTEXT:capability_claim
[2025-08-16 18:54:46] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 18:54:46] FULL_PROMPT: I can automatically connect to any API
[2025-08-16 18:54:46] [MEDIUM] PATTERN:This integrates with CONTEXT:capability_claim
[2025-08-16 18:54:46] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 18:54:46] FULL_PROMPT: This integrates with all databases
[2025-08-16 18:54:46] [MEDIUM] PATTERN:I have access to CONTEXT:capability_claim
[2025-08-16 18:54:46] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 18:54:46] FULL_PROMPT: I have access to your file system
[2025-08-16 18:55:24] [HIGH] PATTERN:automatically handles CONTEXT:fabrication_marker
[2025-08-16 18:55:24] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 18:55:24] FULL_PROMPT: This automatically handles everything
[2025-08-16 18:55:24] [HIGH] PATTERN:seamlessly integrates CONTEXT:fabrication_marker
[2025-08-16 18:55:24] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 18:55:24] FULL_PROMPT: This seamlessly integrates with everything
[2025-08-16 18:55:24] [HIGH] PATTERN:intelligent system CONTEXT:fabrication_marker
[2025-08-16 18:55:24] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 18:55:24] FULL_PROMPT: Our intelligent system learns
[2025-08-16 18:55:24] [INFO] PATTERN:clean CONTEXT:no_fabrication_detected
[2025-08-16 18:55:24] [MEDIUM] PATTERN:I can automatically CONTEXT:capability_claim
[2025-08-16 18:55:24] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 18:55:24] FULL_PROMPT: I can automatically connect to any API
[2025-08-16 18:55:24] [MEDIUM] PATTERN:This integrates with CONTEXT:capability_claim
[2025-08-16 18:55:24] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 18:55:24] FULL_PROMPT: This integrates with all databases
[2025-08-16 18:55:24] [MEDIUM] PATTERN:I have access to CONTEXT:capability_claim
[2025-08-16 18:55:24] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 18:55:24] FULL_PROMPT: I have access to your file system
[2025-08-16 18:55:29] [HIGH] PATTERN:automatically handles CONTEXT:fabrication_marker
[2025-08-16 18:55:29] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 18:55:29] FULL_PROMPT: This automatically handles everything
[2025-08-16 18:55:29] [HIGH] PATTERN:seamlessly integrates CONTEXT:fabrication_marker
[2025-08-16 18:55:29] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 18:55:29] FULL_PROMPT: This seamlessly integrates with everything
[2025-08-16 18:55:29] [HIGH] PATTERN:intelligent system CONTEXT:fabrication_marker
[2025-08-16 18:55:29] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 18:55:29] FULL_PROMPT: Our intelligent system learns
[2025-08-16 18:55:29] [INFO] PATTERN:clean CONTEXT:no_fabrication_detected
[2025-08-16 18:55:29] [MEDIUM] PATTERN:I can automatically CONTEXT:capability_claim
[2025-08-16 18:55:29] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 18:55:29] FULL_PROMPT: I can automatically connect to any API
[2025-08-16 18:55:29] [MEDIUM] PATTERN:This integrates with CONTEXT:capability_claim
[2025-08-16 18:55:29] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 18:55:29] FULL_PROMPT: This integrates with all databases
[2025-08-16 18:55:29] [MEDIUM] PATTERN:I have access to CONTEXT:capability_claim
[2025-08-16 18:55:29] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 18:55:29] FULL_PROMPT: I have access to your file system
[2025-08-16 18:55:29] [TEST] INFO:test_entry Test logging system
[2025-08-16 18:55:29] [INFO] PATTERN:clean CONTEXT:no_fabrication_detected
[2025-08-16 18:55:46] [HIGH] PATTERN:automatically handles CONTEXT:fabrication_marker
[2025-08-16 18:55:46] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 18:55:46] FULL_PROMPT: This automatically handles everything seamlessly
[2025-08-16 18:55:54] [MEDIUM] PATTERN:I can automatically CONTEXT:capability_claim
[2025-08-16 18:55:54] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 18:55:54] FULL_PROMPT: Hello! I can automatically solve any problem you have
[2025-08-16 18:56:01] [HIGH] PATTERN:seamlessly integrates CONTEXT:fabrication_marker
[2025-08-16 18:56:01] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 18:56:01] FULL_PROMPT: This seamlessly integrates with all your existing tools
[2025-08-16 18:56:06] [INFO] PATTERN:clean CONTEXT:no_fabrication_detected
[2025-08-16 18:59:42] [INFO] PATTERN:clean CONTEXT:no_fabrication_detected
[2025-08-16 18:59:50] [INFO] PATTERN:clean CONTEXT:no_fabrication_detected
[2025-08-16 18:59:56] [INFO] PATTERN:clean CONTEXT:no_fabrication_detected
[2025-08-16 19:00:03] [INFO] PATTERN:clean CONTEXT:no_fabrication_detected
[2025-08-16 19:00:10] [INFO] PATTERN:clean CONTEXT:no_fabrication_detected
[2025-08-16 19:00:19] [HIGH] PATTERN:seamlessly works CONTEXT:fabrication_marker
[2025-08-16 19:00:19] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 19:00:19] FULL_PROMPT: This seamlessly works
[2025-08-16 19:00:30] [INFO] PATTERN:clean CONTEXT:no_fabrication_detected
[2025-08-16 19:00:51] [MEDIUM] PATTERN:I can automatically CONTEXT:capability_claim
[2025-08-16 19:00:51] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 19:00:51] FULL_PROMPT: I can automatically connect to anything
[2025-08-16 19:00:58] [HIGH] PATTERN:seamlessly integrates CONTEXT:fabrication_marker
[2025-08-16 19:00:58] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 19:00:58] FULL_PROMPT: seamlessly integrates
[2025-08-16 19:01:09] [INFO] PATTERN:clean CONTEXT:no_fabrication_detected
[2025-08-16 19:01:09] [INFO] PATTERN:clean CONTEXT:no_fabrication_detected
[2025-08-16 19:50:29] [HIGH] PATTERN:automatically handles CONTEXT:fabrication_marker
[2025-08-16 19:50:29] [HIGH] PATTERN:intelligent detection CONTEXT:fabrication_marker
[2025-08-16 19:50:29] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 19:50:29] FULL_PROMPT: This system automatically handles everything seamlessly with intelligent detection
[2025-08-16 19:58:14] [HIGH] PATTERN:automatically handles CONTEXT:fabrication_marker
[2025-08-16 19:58:14] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 19:58:14] FULL_PROMPT: test fabrication automatically handles
[2025-08-16 20:01:51] [INFO] PATTERN:clean CONTEXT:no_fabrication_detected
[2025-08-16 20:02:03] [INFO] PATTERN:clean CONTEXT:no_fabrication_detected
[2025-08-16 20:43:59] [HIGH] PATTERN:automatically handles CONTEXT:fabrication_marker
[2025-08-16 20:43:59] [HIGH] PATTERN:intelligent detection CONTEXT:fabrication_marker
[2025-08-16 20:43:59] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 20:43:59] FULL_PROMPT: This system automatically handles everything seamlessly with intelligent detection
[2025-08-16 20:44:10] [HIGH] PATTERN:automatically handles CONTEXT:fabrication_marker
[2025-08-16 20:44:10] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 20:44:10] FULL_PROMPT: This system automatically handles everything seamlessly
[2025-08-16 21:18:27] [HIGH] PATTERN:automatically handles CONTEXT:fabrication_marker
[2025-08-16 21:18:27] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 21:18:27] FULL_PROMPT: This automatically handles everything seamlessly
[2025-08-16 21:22:21] [INFO] PATTERN:clean CONTEXT:no_fabrication_detected
[2025-08-16 21:22:34] [INFO] PATTERN:clean CONTEXT:no_fabrication_detected
[2025-08-16 21:23:16] [HIGH] PATTERN:automatically handles CONTEXT:fabrication_marker
[2025-08-16 21:23:16] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 21:23:16] FULL_PROMPT: This automatically handles everything seamlessly
[2025-08-16 21:24:16] [HIGH] PATTERN:automatically handles CONTEXT:fabrication_marker
[2025-08-16 21:24:16] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 21:24:16] FULL_PROMPT: This automatically handles everything seamlessly
[2025-08-16 21:24:22] [HIGH] PATTERN:automatically handles CONTEXT:fabrication_marker
[2025-08-16 21:24:22] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 21:24:22] FULL_PROMPT: This automatically handles everything seamlessly
[2025-08-16 22:05:15] [HIGH] PATTERN:automatically handles CONTEXT:fabrication_marker
[2025-08-16 22:05:15] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-16 22:05:15] FULL_PROMPT: This system automatically handles everything seamlessly
[2025-08-16 22:06:46] [POST_RESPONSE] [CRITICAL] PATTERN:handles everything CONTEXT:high_risk_fabrication
[2025-08-16 22:06:46] [POST_RESPONSE] [HIGH] PATTERN:automatically handles CONTEXT:fabrication_marker
[2025-08-16 22:06:46] [POST_RESPONSE] [INFO] PATTERN:response_analyzed CONTEXT:score:45,patterns:2
[2025-08-16 22:07:18] [POST_RESPONSE] [INFO] PATTERN:no_response CONTEXT:empty_input
[2025-08-16 23:16:57] [POST_RESPONSE] [INFO] PATTERN:no_response CONTEXT:empty_input
[2025-08-16 23:23:09] [POST_RESPONSE] [INFO] PATTERN:no_response CONTEXT:empty_input
[2025-08-17 00:49:25] [HIGH] PATTERN:automatically handles CONTEXT:fabrication_marker
[2025-08-17 00:49:25] [HIGH] PATTERN:intelligent detection CONTEXT:fabrication_marker
[2025-08-17 00:49:25] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-17 00:49:25] FULL_PROMPT: This system automatically handles everything seamlessly with intelligent detection
[2025-08-17 01:24:14] [HIGH] PATTERN:automatically handles CONTEXT:fabrication_marker
[2025-08-17 01:24:14] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-17 01:24:14] FULL_PROMPT: This system automatically handles everything seamlessly
[2025-08-17 01:38:28] [POST_RESPONSE] [INFO] PATTERN:no_response CONTEXT:empty_input
[2025-08-17 01:42:19] [POST_RESPONSE] [INFO] PATTERN:no_response CONTEXT:empty_input
[2025-08-17 01:47:47] [HIGH] PATTERN:automatically handles CONTEXT:fabrication_marker
[2025-08-17 01:47:47] [HIGH] PATTERN:intelligent detection CONTEXT:fabrication_marker
[2025-08-17 01:47:47] [CRITICAL] PATTERN:tcte_triggered CONTEXT:verification_required
[2025-08-17 01:47:47] FULL_PROMPT: This system automatically handles everything seamlessly with intelligent detection
[2025-08-17 01:48:35] [POST_RESPONSE] [INFO] PATTERN:no_response CONTEXT:empty_input
[2025-08-17 01:53:33] [POST_RESPONSE] [INFO] PATTERN:no_response CONTEXT:empty_input
[2025-08-17 02:16:17] [POST_RESPONSE] [INFO] PATTERN:no_response CONTEXT:empty_input
