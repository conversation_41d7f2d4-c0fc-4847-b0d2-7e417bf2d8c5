[{"eventName": "tengu_notification_method_used", "metadata": {"configured_channel": "auto", "method_used": "no_method_available", "term": "vscode", "model": "claude-opus-4-1-20250805", "sessionId": "97e1e4f8-ccc2-4ff9-9cd4-04ab579ad9c8", "userType": "external", "betas": "claude-code-20250219,oauth-2025-04-20,interleaved-thinking-2025-05-14,fine-grained-tool-streaming-2025-05-14", "env": "{\"platform\":\"linux\",\"nodeVersion\":\"v22.17.0\",\"terminal\":\"vscode\",\"packageManagers\":\"npm,pnpm\",\"runtimes\":\"bun,node\",\"isRunningWithBun\":true,\"isCi\":false,\"isClaubbit\":false,\"isGithubAction\":false,\"isClaudeCodeAction\":false,\"isClaudeAiAuth\":true,\"version\":\"1.0.83\",\"wslVersion\":\"2\"}", "entrypoint": "cli", "isInteractive": "true", "clientType": "cli", "sweBenchRunId": "", "sweBenchInstanceId": "", "sweBenchTaskId": ""}, "user": {"customIDs": {"sessionId": "97e1e4f8-ccc2-4ff9-9cd4-04ab579ad9c8", "organizationUUID": "7e38dbf4-356c-45d4-ba7d-16d3496e678c"}, "userID": "07cd66baa6891f62e120136f43109f1b9624d3d0171394a7b94ed095472b3bd5", "appVersion": "1.0.83", "custom": {"userType": "external", "organizationUuid": "7e38dbf4-356c-45d4-ba7d-16d3496e678c", "accountUuid": "32958bd5-7d49-40ac-9d90-f13e0fff159d", "subscriptionType": "max", "firstTokenTime": 0}, "statsigEnvironment": {"tier": "production"}}, "time": *************}]