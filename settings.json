{"$schema": "https://json.schemastore.org/claude-code-settings.json", "env": {}, "permissions": {"allow": ["*"], "deny": ["<PERSON><PERSON>(sudo shutdown:*)", "<PERSON><PERSON>(sudo reboot:*)", "Bash(rm -rf /:*)", "Bash(rm -rf /.*)", "Bash(format:*)", "Bash(fdisk:*)", "Bash(dd if=:*)", "Bash(mkfs:*)"]}, "hooks": {"PreToolUse": [{"matcher": "Read", "hooks": [{"type": "command", "command": "/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh /home/<USER>/.claude/hooks/pdf-api-error-preventer.sh"}]}, {"matcher": "Edit", "hooks": [{"type": "command", "command": "/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh echo '📝 SAFETY: <PERSON> is editing a file'"}]}, {"matcher": "MultiEdit", "hooks": [{"type": "command", "command": "/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh echo '📝 SAFETY: <PERSON> is multi-editing files'"}]}, {"matcher": "<PERSON><PERSON>", "hooks": [{"type": "command", "command": "/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh echo '💻 SAFETY: <PERSON> is running a bash command'"}]}, {"matcher": "Write", "hooks": [{"type": "command", "command": "/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh echo '📝 SAFETY: <PERSON> is creating/writing a file'"}]}, {"matcher": "TodoWrite", "hooks": [{"type": "command", "command": "/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh bash /home/<USER>/.claude/hooks/dynamic_claude_injector.sh"}]}, {"matcher": "", "hooks": [{"type": "command", "command": "/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh bash /home/<USER>/.claude/hooks/dynamic_claude_injector.sh"}]}, {"matcher": "WebSearch", "hooks": [{"type": "command", "command": "/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh /home/<USER>/.claude/hooks/web-search-date-enforcer.sh"}]}], "PostToolUse": [{"matcher": "*", "hooks": [{"type": "command", "command": "/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh /home/<USER>/.claude/hooks/universal-api-error-recovery.sh"}]}, {"matcher": "mcp__mermaid__generate|mcp__wslsnapit__take_screenshot", "hooks": [{"type": "command", "command": "/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh /home/<USER>/.claude/hooks/mcp-output-validator.sh"}]}, {"matcher": "Edit|MultiEdit|Write", "hooks": [{"type": "command", "command": "/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh echo '✅ SAFETY: File operation completed'"}]}], "UserPromptSubmit": [{"hooks": [{"type": "command", "command": "/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh /home/<USER>/.claude/hooks/global_claude_enforcer.sh"}, {"type": "command", "command": "/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh /home/<USER>/.claude/hooks/user_prompt_pre_validator.sh"}, {"type": "command", "command": "/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh /home/<USER>/.claude/hooks/fabrication-detector.sh"}, {"type": "command", "command": "/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh /home/<USER>/.claude/hooks/date-time-proactive-validator.sh"}, {"type": "command", "command": "/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh /home/<USER>/.claude/hooks/date-path-validator.sh"}, {"type": "command", "command": "/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh /home/<USER>/.claude/hooks/date-verification-compliance-expert.sh"}, {"type": "command", "command": "/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh /home/<USER>/.claude/hooks/context_aware_directory_hook.sh"}, {"type": "command", "command": "/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh /home/<USER>/.claude/hooks/ai_first_methodology_validator.sh"}, {"type": "command", "command": "/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh /home/<USER>/.claude/hooks/context7-reminder-hook.sh"}, {"type": "command", "command": "/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh /home/<USER>/.claude/hooks/memory_enforcer.sh"}, {"type": "command", "command": "/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh /home/<USER>/.claude/hooks/memory_session_recovery.sh"}, {"type": "command", "command": "/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh /home/<USER>/.claude/hooks/compliance-expert-update-enforcer.sh"}, {"type": "command", "command": "/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh /home/<USER>/.claude/hooks/tcte-primary-verification.sh"}, {"type": "command", "command": "/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh /home/<USER>/.claude/hooks/tcte-secondary-verification.sh"}, {"type": "command", "command": "/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh /home/<USER>/.claude/hooks/tcte-tertiary-verification.sh"}]}, {"hooks": [{"type": "command", "command": "/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh /home/<USER>/.claude/hooks/browser_automation_checker.sh"}]}, {"hooks": [{"type": "command", "command": "/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh /home/<USER>/.claude/hooks/mcp-global-validator.sh"}]}], "Stop": [{"hooks": [{"type": "command", "command": "/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh /home/<USER>/.claude/hooks/post-response-fabrication-detector.sh"}, {"type": "command", "command": "/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh /home/<USER>/.claude/hooks/date-validation-stop-hook.sh"}, {"type": "command", "command": "/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh /home/<USER>/.claude/hooks/conversation_backup.sh"}, {"type": "command", "command": "/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh echo '📋 SAFETY: Session ending'"}]}], "userPromptSubmit": ["hook-execution-monitor.sh"]}, "feedbackSurveyState": {"lastShownTime": 1754076279000}, "pdfHandling": {"enabled": true, "autonomyLevel": "smart", "preferFormat": "markdown", "cacheEnabled": true, "cacheRetentionDays": 7, "cacheMaxSizeMb": 500, "autoCleanup": true, "extractionMethods": {"primary": "pdftotext", "fallback": ["pypdf2", "desktop-commander"], "ocr": "tesseract"}, "typeDetection": true, "performanceTracking": true, "discussionMode": {"respectRequests": true, "logDecisions": true}}, "model": "opusplan"}