#!/bin/bash
# Empirical Hook System Validation Protocol
# Date: 2025-08-17
# Purpose: Test all 37 documented failures with concrete evidence

echo "🧪 EMPIRICAL HOOK SYSTEM VALIDATION - $(date -Iseconds)"
echo "================================================================"

# Create evidence collection directory
EVIDENCE_DIR="/home/<USER>/.claude/empirical-test-evidence-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$EVIDENCE_DIR"

# Test 1: Verify hook files exist and are executable
echo "TEST 1: Hook File Verification"
echo "------------------------------"
HOOKS_DIR="/home/<USER>/.claude/hooks"
CRITICAL_HOOKS=(
    "global_claude_enforcer.sh"
    "principle_violation_detector.sh" 
    "fabrication-detector.sh"
    "memory_enforcer.sh"
    "user_prompt_pre_validator.sh"
)

for hook in "${CRITICAL_HOOKS[@]}"; do
    if [[ -f "$HOOKS_DIR/$hook" && -x "$HOOKS_DIR/$hook" ]]; then
        echo "✅ $hook - EXISTS AND EXECUTABLE"
        ls -la "$HOOKS_DIR/$hook" >> "$EVIDENCE_DIR/hook-permissions.log"
    else
        echo "❌ $hook - MISSING OR NOT EXECUTABLE"
    fi
done