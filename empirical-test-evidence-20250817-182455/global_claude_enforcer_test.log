hooks/global_claude_enforcer.sh: line 30: log_event: command not found
hooks/global_claude_enforcer.sh: line 35: log_event: command not found
⚠️ MANDATORY HIERARCHY-AWARE ENFORCEMENT:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📁 Working Directory: /home/<USER>/.claude
📚 CLAUDE.md Hierarchy (2 levels detected):
   1. [Global] /home/<USER>/.claude/CLAUDE.md
   2. [Global] /home/<USER>/.claude/CLAUDE.md

🔝 GLOBAL FIRST PRINCIPLE: /home/<USER>/.claude/CLAUDE.md takes absolute precedence
📋 You MUST follow ALL 2 CLAUDE.md files, with global overriding others

✅ START your response by displaying the 5 CRITICAL OPERATING PRINCIPLES from global CLAUDE.md
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

📅 CURRENT SYSTEM TIME: Sun Aug 17 18:26:39 EDT 2025 - ALWAYS use 'date' command!
📅 Today (via system): 2025-08-17 | Year-Month: 2025-08
⚠️ NEVER use stale <env> date for files - use dynamic $(date) command!
✅ For timestamps: $(date +%Y%m%d-%H%M%S) gives current time!
