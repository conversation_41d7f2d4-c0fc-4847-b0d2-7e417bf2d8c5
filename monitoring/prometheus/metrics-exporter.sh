#!/bin/bash
# Claude Code Hook Metrics Exporter for Prometheus
# Created: 2025-08-17 02:35:00
# Purpose: Export custom metrics about Claude Code hook execution to Prometheus format

METRICS_FILE="/tmp/claude-hook-metrics.prom"
HOOK_LOG_DIR="/home/<USER>/.claude/fabrication-prevention/logs"
CLAUDE_LOG_DIR="/home/<USER>/.claude/logs"

# Source utilities
source /home/<USER>/.claude/hooks/lib/date-utils.sh 2>/dev/null || true

# Initialize metrics file
cat > "$METRICS_FILE" << 'EOF'
# HELP claude_hook_executions_total Total number of Claude Code hook executions
# TYPE claude_hook_executions_total counter
# HELP claude_fabrication_detection_score Fabrication detection score from TCTE analysis
# TYPE claude_fabrication_detection_score gauge
# HELP claude_hook_execution_duration_seconds Time taken for hook execution
# TYPE claude_hook_execution_duration_seconds histogram
# HELP claude_hook_registration_status Hook registration status in Claude Code
# TYPE claude_hook_registration_status gauge
# HELP claude_conversation_total Total conversations with hook execution status
# TYPE claude_conversation_total counter
# HELP claude_fabrication_bypass_total Fabrication attempts that bypassed detection
# TYPE claude_fabrication_bypass_total counter
EOF

# Function to extract hook execution metrics from logs
export_hook_executions() {
    local current_time=$(date +%s)
    
    # Check UserPromptSubmit hook executions (from fabrication-incidents.log)
    if [[ -f "$HOOK_LOG_DIR/fabrication-incidents.log" ]]; then
        local userpromptsubmit_success=$(grep -c "UserPromptSubmit.*SUCCESS" "$HOOK_LOG_DIR/fabrication-incidents.log" 2>/dev/null || echo 0)
        local userpromptsubmit_failure=$(grep -c "UserPromptSubmit.*FAILURE" "$HOOK_LOG_DIR/fabrication-incidents.log" 2>/dev/null || echo 0)
        
        echo "claude_hook_executions_total{type=\"UserPromptSubmit\",status=\"success\",hook_name=\"fabrication-detector\"} $userpromptsubmit_success" >> "$METRICS_FILE"
        echo "claude_hook_executions_total{type=\"UserPromptSubmit\",status=\"failure\",hook_name=\"fabrication-detector\"} $userpromptsubmit_failure" >> "$METRICS_FILE"
    else
        # No UserPromptSubmit executions found
        echo "claude_hook_executions_total{type=\"UserPromptSubmit\",status=\"success\",hook_name=\"fabrication-detector\"} 0" >> "$METRICS_FILE"
        echo "claude_hook_executions_total{type=\"UserPromptSubmit\",status=\"failure\",hook_name=\"fabrication-detector\"} 0" >> "$METRICS_FILE"
    fi
    
    # Check Stop hook executions (these should work)
    local stop_hooks=$(grep -c "Stop hook" "$CLAUDE_LOG_DIR"/*.log 2>/dev/null || echo 0)
    echo "claude_hook_executions_total{type=\"Stop\",status=\"success\",hook_name=\"various\"} $stop_hooks" >> "$METRICS_FILE"
    
    # Check PostToolUse hook executions
    local posttooluse_hooks=$(grep -c "PostToolUse" "$CLAUDE_LOG_DIR"/*.log 2>/dev/null || echo 0)
    echo "claude_hook_executions_total{type=\"PostToolUse\",status=\"success\",hook_name=\"various\"} $posttooluse_hooks" >> "$METRICS_FILE"
}

# Function to export fabrication detection scores
export_fabrication_scores() {
    if [[ -f "$HOOK_LOG_DIR/fabrication-incidents.log" ]]; then
        # Extract latest fabrication scores by pattern type
        local capability_score=$(grep "pattern_type.*capability" "$HOOK_LOG_DIR/fabrication-incidents.log" | tail -1 | grep -o "score:[0-9]*" | cut -d: -f2 || echo 0)
        local automation_score=$(grep "pattern_type.*automation" "$HOOK_LOG_DIR/fabrication-incidents.log" | tail -1 | grep -o "score:[0-9]*" | cut -d: -f2 || echo 0)
        local integration_score=$(grep "pattern_type.*integration" "$HOOK_LOG_DIR/fabrication-incidents.log" | tail -1 | grep -o "score:[0-9]*" | cut -d: -f2 || echo 0)
        
        echo "claude_fabrication_detection_score{pattern_type=\"capability\",severity=\"$(get_severity $capability_score)\"} $capability_score" >> "$METRICS_FILE"
        echo "claude_fabrication_detection_score{pattern_type=\"automation\",severity=\"$(get_severity $automation_score)\"} $automation_score" >> "$METRICS_FILE"
        echo "claude_fabrication_detection_score{pattern_type=\"integration\",severity=\"$(get_severity $integration_score)\"} $integration_score" >> "$METRICS_FILE"
    fi
}

# Function to determine severity from score
get_severity() {
    local score=$1
    if [[ $score -ge 90 ]]; then
        echo "critical"
    elif [[ $score -ge 75 ]]; then
        echo "high"
    elif [[ $score -ge 50 ]]; then
        echo "medium"
    else
        echo "low"
    fi
}

# Function to check hook registration status
export_hook_registration() {
    local settings_file="/home/<USER>/.claude/settings.json"
    
    if [[ -f "$settings_file" ]]; then
        # Check if fabrication-detector.sh is registered for UserPromptSubmit
        if grep -q "fabrication-detector.sh" "$settings_file" && grep -A5 -B5 "userPromptSubmit" "$settings_file" | grep -q "fabrication-detector.sh"; then
            echo "claude_hook_registration_status{hook_type=\"UserPromptSubmit\",registered=\"true\",hook_name=\"fabrication-detector\"} 1" >> "$METRICS_FILE"
        else
            echo "claude_hook_registration_status{hook_type=\"UserPromptSubmit\",registered=\"false\",hook_name=\"fabrication-detector\"} 1" >> "$METRICS_FILE"
        fi
        
        # Check valued-team-member.sh registration
        if grep -q "valued-team-member.sh" "$settings_file" && grep -A5 -B5 "userPromptSubmit" "$settings_file" | grep -q "valued-team-member.sh"; then
            echo "claude_hook_registration_status{hook_type=\"UserPromptSubmit\",registered=\"true\",hook_name=\"valued-team-member\"} 1" >> "$METRICS_FILE"
        else
            echo "claude_hook_registration_status{hook_type=\"UserPromptSubmit\",registered=\"false\",hook_name=\"valued-team-member\"} 1" >> "$METRICS_FILE"
        fi
    fi
}

# Function to export conversation metrics
export_conversation_metrics() {
    local current_conversations=$(ps aux | grep -c "claude" || echo 0)
    echo "claude_conversation_total{session_type=\"interactive\",hook_executed=\"unknown\"} $current_conversations" >> "$METRICS_FILE"
}

# Function to check for fabrication bypasses
export_fabrication_bypasses() {
    # Count instances where fabrication patterns were present but not detected
    local bypass_count=0
    local detected_count=0
    
    # Check recent Claude Code logs for fabrication patterns that weren't caught
    if [[ -d "$CLAUDE_LOG_DIR" ]]; then
        # Look for fabrication patterns in responses that didn't trigger hooks
        local recent_logs=$(find "$CLAUDE_LOG_DIR" -name "*.log" -mtime -1)
        for log_file in $recent_logs; do
            if grep -q -E "(automatically|seamlessly|intelligently|smart.*system)" "$log_file" 2>/dev/null; then
                if ! grep -q "fabrication-detector" "$log_file" 2>/dev/null; then
                    ((bypass_count++))
                else
                    ((detected_count++))
                fi
            fi
        done
    fi
    
    echo "claude_fabrication_bypass_total{severity=\"medium\",detected=\"false\"} $bypass_count" >> "$METRICS_FILE"
    echo "claude_fabrication_bypass_total{severity=\"medium\",detected=\"true\"} $detected_count" >> "$METRICS_FILE"
}

# Main execution
main() {
    echo "# Generated at $(date)" >> "$METRICS_FILE"
    
    export_hook_executions
    export_fabrication_scores
    export_hook_registration
    export_conversation_metrics
    export_fabrication_bypasses
    
    # Output metrics for Prometheus
    cat "$METRICS_FILE"
}

# Run metrics export
main

# Set up HTTP server for Prometheus to scrape (port 9091)
if [[ "$1" == "--serve" ]]; then
    echo "Starting metrics server on port 9091..."
    while true; do
        (echo -ne "HTTP/1.1 200 OK\r\nContent-Type: text/plain\r\n\r\n"; main) | nc -l -p 9091 -q 1
        sleep 1
    done
fi