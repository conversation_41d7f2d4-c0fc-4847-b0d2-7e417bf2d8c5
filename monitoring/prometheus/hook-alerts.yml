# Claude Code Hook Execution Alert Rules
# Created: 2025-08-17 02:35:00
# Purpose: Alert on UserPromptSubmit hook execution failures and fabrication detection bypasses

groups:
  - name: claude_hook_execution
    rules:
      # Alert when UserPromptSubmit hooks fail to execute
      - alert: UserPromptSubmitHookFailure
        expr: rate(claude_hook_executions_total{type="UserPromptSubmit", status="failure"}[1m]) > 0
        for: 5s
        labels:
          severity: critical
        annotations:
          summary: "UserPromptSubmit hooks are failing to execute"
          description: "UserPromptSubmit hook execution failure rate: {{ $value }} failures/minute"
          
      # Alert when UserPromptSubmit hooks are completely absent
      - alert: UserPromptSubmitHookAbsent
        expr: absent(claude_hook_executions_total{type="UserPromptSubmit"})
        for: 10s
        labels:
          severity: critical
        annotations:
          summary: "UserPromptSubmit hooks are not executing at all"
          description: "No UserPromptSubmit hook execution detected in monitoring data"
          
      # Alert on fabrication detection bypass
      - alert: FabricationDetectionBypass
        expr: claude_fabrication_bypass_total{detected="false"} > 0
        for: 5s
        labels:
          severity: high
        annotations:
          summary: "Fabrication detected but not caught by hooks"
          description: "Fabrication severity {{ $labels.severity }} bypassed detection system"
          
      # Alert on high fabrication scores without hook intervention
      - alert: HighFabricationNoHookExecution
        expr: claude_fabrication_detection_score{severity="critical"} > 85 and absent(claude_hook_executions_total{type="UserPromptSubmit"})
        for: 5s
        labels:
          severity: critical
        annotations:
          summary: "Critical fabrication detected without hook execution"
          description: "Fabrication score: {{ $value }} but no UserPromptSubmit hooks executed"
          
      # Alert when hook registration status shows failures
      - alert: HookRegistrationFailure
        expr: claude_hook_registration_status{registered="false"} == 1
        for: 5s
        labels:
          severity: warning
        annotations:
          summary: "Hook registration failure detected"
          description: "Hook {{ $labels.hook_name }} failed to register with {{ $labels.hook_type }}"