# Prometheus Configuration for Claude Code Hook Monitoring
# Created: 2025-08-17 02:35:00
# Purpose: Real-time monitoring of UserPromptSubmit hook execution failures

global:
  scrape_interval: 1s  # High frequency for real-time hook monitoring
  evaluation_interval: 1s
  external_labels:
    monitor: 'claude-code-hooks'

# Alerting rules for hook execution failures
rule_files:
  - "/home/<USER>/.claude/monitoring/prometheus/hook-alerts.yml"

# Alert manager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets: []

# Scrape configurations for Claude Code components
scrape_configs:
  # Claude Code hook execution metrics
  - job_name: 'claude-hooks'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 500ms
    metrics_path: /metrics
    
  # File-based metrics from hook execution logs
  - job_name: 'claude-hook-logs'
    static_configs:
      - targets: ['localhost:9091']
    scrape_interval: 1s
    
  # Node exporter for system metrics
  - job_name: 'node'
    static_configs:
      - targets: ['localhost:9100']

# Custom metric definitions for Claude Code hooks
# These will be generated by our monitoring scripts:

# claude_hook_executions_total{type="UserPromptSubmit|Stop|PostToolUse", status="success|failure", hook_name="fabrication-detector"}
# claude_fabrication_detection_score{pattern_type="capability|automation|integration", severity="low|medium|high|critical"}
# claude_hook_execution_duration_seconds{hook_type="UserPromptSubmit", hook_name="fabrication-detector"}
# claude_hook_registration_status{hook_type="UserPromptSubmit", registered="true|false", hook_name="fabrication-detector"}
# claude_conversation_total{session_type="interactive|command", hook_executed="true|false"}
# claude_fabrication_bypass_total{severity="low|medium|high|critical", detected="true|false"}