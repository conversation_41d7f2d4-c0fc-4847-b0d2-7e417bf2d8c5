#!/bin/bash
# Hook Execution Trace - Debug UserPromptSubmit Hook Failure
# Created: 2025-08-17 02:42:00
# Purpose: Trace the exact execution path and failure point of UserPromptSubmit hooks

TRACE_LOG="/home/<USER>/.claude/monitoring/logs/hook-execution-trace.log"

log_trace() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S.%3N')] TRACE: $*" | tee -a "$TRACE_LOG"
}

test_hook_execution_chain() {
    log_trace "TESTING HOOK EXECUTION CHAIN"
    log_trace "=============================="
    
    # Test 1: Direct hook execution (should work)
    log_trace "TEST 1: Direct fabrication-detector.sh execution"
    if echo "automatically handles" | /home/<USER>/.claude/hooks/fabrication-detector.sh > /tmp/direct-test.log 2>&1; then
        log_trace "✅ Direct execution: SUCCESS"
        log_trace "Output: $(cat /tmp/direct-test.log)"
    else
        log_trace "❌ Direct execution: FAILED"
        log_trace "Error: $(cat /tmp/direct-test.log)"
    fi
    
    # Test 2: Telemetry wrapper execution (may fail)
    log_trace ""
    log_trace "TEST 2: Telemetry wrapper + fabrication-detector.sh execution"
    if echo "automatically handles" | /home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh /home/<USER>/.claude/hooks/fabrication-detector.sh > /tmp/wrapper-test.log 2>&1; then
        log_trace "✅ Wrapper execution: SUCCESS"
        log_trace "Output: $(cat /tmp/wrapper-test.log)"
    else
        log_trace "❌ Wrapper execution: FAILED"
        log_trace "Error: $(cat /tmp/wrapper-test.log)"
    fi
    
    # Test 3: Full command chain from settings.json
    log_trace ""
    log_trace "TEST 3: Full command chain from settings.json"
    local full_command="/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh /home/<USER>/.claude/hooks/fabrication-detector.sh"
    if echo "automatically handles" | bash -c "$full_command" > /tmp/full-chain-test.log 2>&1; then
        log_trace "✅ Full chain execution: SUCCESS"
        log_trace "Output: $(cat /tmp/full-chain-test.log)"
    else
        log_trace "❌ Full chain execution: FAILED" 
        log_trace "Error: $(cat /tmp/full-chain-test.log)"
    fi
    
    # Test 4: Check environment variables impact
    log_trace ""
    log_trace "TEST 4: Environment variables impact test"
    
    # Save current env
    env > /tmp/env-before.txt
    
    # Source telemetry environment
    export CLAUDE_CODE_ENABLE_TELEMETRY=1
    export OTEL_METRICS_EXPORTER=otlp
    export OTEL_LOGS_EXPORTER=otlp
    export OTEL_EXPORTER_OTLP_PROTOCOL=http/protobuf
    export OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4318
    export OTEL_METRIC_EXPORT_INTERVAL=10000
    export OTEL_LOGS_EXPORT_INTERVAL=5000
    export OTEL_RESOURCE_ATTRIBUTES="service.name=claude-code-cicd,service.version=1.0.0,environment=ci-cd-pipeline,component=hooks"
    export OTEL_LOG_USER_PROMPTS=0
    export OTEL_METRICS_INCLUDE_SESSION_ID=true
    export OTEL_METRICS_INCLUDE_VERSION=true
    export OTEL_METRICS_INCLUDE_ACCOUNT_UUID=false
    
    if echo "automatically handles" | /home/<USER>/.claude/hooks/fabrication-detector.sh > /tmp/env-test.log 2>&1; then
        log_trace "✅ With telemetry env: SUCCESS"
        log_trace "Output: $(cat /tmp/env-test.log)"
    else
        log_trace "❌ With telemetry env: FAILED"
        log_trace "Error: $(cat /tmp/env-test.log)"
    fi
}

test_stdin_vs_args() {
    log_trace ""
    log_trace "TESTING STDIN vs COMMAND ARGS"
    log_trace "=============================="
    
    # Test with command arguments
    log_trace "TEST: Command arguments method"
    if /home/<USER>/.claude/hooks/fabrication-detector.sh "automatically handles" > /tmp/args-test.log 2>&1; then
        log_trace "✅ Command args: SUCCESS"
        log_trace "Output: $(cat /tmp/args-test.log)"
    else
        log_trace "❌ Command args: FAILED"
        log_trace "Error: $(cat /tmp/args-test.log)"
    fi
    
    # Test with STDIN
    log_trace ""
    log_trace "TEST: STDIN method"
    if echo "automatically handles" | /home/<USER>/.claude/hooks/fabrication-detector.sh > /tmp/stdin-test.log 2>&1; then
        log_trace "✅ STDIN: SUCCESS"
        log_trace "Output: $(cat /tmp/stdin-test.log)"
    else
        log_trace "❌ STDIN: FAILED"
        log_trace "Error: $(cat /tmp/stdin-test.log)"
    fi
    
    # Test JSON input (simulate Claude Code input)
    log_trace ""
    log_trace "TEST: JSON input method (simulating Claude Code)"
    local json_input='{"session_id":"test","prompt":"automatically handles","cwd":"/home/<USER>/.claude"}'
    if echo "$json_input" | /home/<USER>/.claude/hooks/fabrication-detector.sh > /tmp/json-test.log 2>&1; then
        log_trace "✅ JSON input: SUCCESS"
        log_trace "Output: $(cat /tmp/json-test.log)"
    else
        log_trace "❌ JSON input: FAILED"
        log_trace "Error: $(cat /tmp/json-test.log)"
    fi
}

check_hook_input_handling() {
    log_trace ""
    log_trace "CHECKING HOOK INPUT HANDLING"
    log_trace "============================"
    
    # Examine how fabrication-detector.sh handles input
    log_trace "Analyzing fabrication-detector.sh input handling..."
    
    # Look for input handling logic
    local input_logic=$(grep -n -A5 -B5 "stdin\|STDIN\|\$1\|\$@" /home/<USER>/.claude/hooks/fabrication-detector.sh 2>/dev/null || echo "No input handling found")
    log_trace "Input handling code:"
    echo "$input_logic" | tee -a "$TRACE_LOG"
}

simulate_claude_code_hook_call() {
    log_trace ""
    log_trace "SIMULATING CLAUDE CODE HOOK CALL"
    log_trace "================================="
    
    # Simulate what Claude Code would actually do
    log_trace "Simulating actual Claude Code UserPromptSubmit hook execution..."
    
    # Create a test prompt similar to what Claude Code would generate
    local test_prompt="Please help me create a system that automatically handles user requests"
    
    # Try to call hook exactly as Claude Code would
    local hook_command="/home/<USER>/.claude/ci-cd-pipeline/observability/telemetry-wrapper.sh /home/<USER>/.claude/hooks/fabrication-detector.sh"
    
    # Method 1: With echo (most likely)
    log_trace "Method 1: Echo to hook"
    if echo "$test_prompt" | bash -c "$hook_command" > /tmp/claude-sim-1.log 2>&1; then
        log_trace "✅ Claude simulation 1: SUCCESS"
    else
        log_trace "❌ Claude simulation 1: FAILED"
        log_trace "Error: $(cat /tmp/claude-sim-1.log)"
    fi
    
    # Method 2: As command argument
    log_trace "Method 2: Command argument"
    if bash -c "$hook_command" "$test_prompt" > /tmp/claude-sim-2.log 2>&1; then
        log_trace "✅ Claude simulation 2: SUCCESS"
    else
        log_trace "❌ Claude simulation 2: FAILED"
        log_trace "Error: $(cat /tmp/claude-sim-2.log)"
    fi
    
    # Method 3: With JSON (if Claude Code uses structured input)
    log_trace "Method 3: JSON input"
    local json_data='{"prompt":"'"$test_prompt"'","session_id":"test-session","cwd":"/home/<USER>/.claude"}'
    if echo "$json_data" | bash -c "$hook_command" > /tmp/claude-sim-3.log 2>&1; then
        log_trace "✅ Claude simulation 3: SUCCESS"
    else
        log_trace "❌ Claude simulation 3: FAILED"
        log_trace "Error: $(cat /tmp/claude-sim-3.log)"
    fi
}

check_claude_code_hook_invocation() {
    log_trace ""
    log_trace "CHECKING CLAUDE CODE HOOK INVOCATION"
    log_trace "===================================="
    
    # Check if there are any Claude Code logs that show how hooks are called
    log_trace "Searching for Claude Code hook invocation patterns..."
    
    # Look for hook execution in various log locations
    local log_locations=(
        "/home/<USER>/.vscode-server/data/logs/20250817T015908/exthost1/Anthropic.claude-code/"
        "/home/<USER>/.claude/hooks/logs/"
        "/tmp/"
        "/var/log/"
    )
    
    for location in "${log_locations[@]}"; do
        if [[ -d "$location" ]]; then
            log_trace "Searching in: $location"
            local hook_calls=$(find "$location" -name "*.log" -exec grep -l "hook\|UserPromptSubmit" {} \; 2>/dev/null | head -3)
            if [[ -n "$hook_calls" ]]; then
                log_trace "Found hook-related logs:"
                echo "$hook_calls" | tee -a "$TRACE_LOG"
                
                # Show recent entries
                for log_file in $hook_calls; do
                    log_trace "Recent entries from $log_file:"
                    tail -5 "$log_file" 2>/dev/null | sed 's/^/  /' | tee -a "$TRACE_LOG"
                done
            fi
        fi
    done
}

main() {
    log_trace "CLAUDE CODE HOOK EXECUTION FAILURE ANALYSIS"
    log_trace "==========================================="
    log_trace "Analysis started: $(date)"
    log_trace ""
    
    test_hook_execution_chain
    test_stdin_vs_args
    check_hook_input_handling
    simulate_claude_code_hook_call
    check_claude_code_hook_invocation
    
    log_trace ""
    log_trace "ANALYSIS COMPLETED: $(date)"
    log_trace "Full trace log: $TRACE_LOG"
}

# Run analysis
main