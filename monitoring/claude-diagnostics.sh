#!/bin/bash
# Claude Code Native Diagnostics Setup
# Created: 2025-08-17 02:35:00
# Purpose: Enable comprehensive Claude Code logging and diagnostics for hook execution failure analysis

CLAUDE_HOME="/home/<USER>/.claude"
DIAGNOSTIC_LOG="$CLAUDE_HOME/monitoring/logs/claude-diagnostics.log"
BACKUP_TIME=$(date +%Y%m%d_%H%M%S)

# Source utilities
source "$CLAUDE_HOME/hooks/lib/date-utils.sh" 2>/dev/null || true

log_diagnostic() {
    echo "[$(get_timestamp)] DIAGNOSTIC: $*" | tee -a "$DIAGNOSTIC_LOG"
}

# Create diagnostic backup of current system state
create_system_backup() {
    log_diagnostic "Creating system state backup: $BACKUP_TIME"
    
    # Backup critical files
    cp "$CLAUDE_HOME/settings.json" "$CLAUDE_HOME/settings.json.backup.$BACKUP_TIME"
    cp "$CLAUDE_HOME/hooks/fabrication-detector.sh" "$CLAUDE_HOME/hooks/fabrication-detector.sh.backup.$BACKUP_TIME"
    cp -r "$CLAUDE_HOME/fabrication-prevention/logs/" "$CLAUDE_HOME/fabrication-prevention/logs.backup.$BACKUP_TIME/"
    
    # Initialize diagnostic environment
    rm -f /tmp/hook-test.log
    echo "$(get_timestamp)" > /tmp/diagnostic-start-time.log
    
    # Log current hook permissions and configuration
    log_diagnostic "Hook file permissions:"
    find "$CLAUDE_HOME/hooks/" -name "*.sh" -exec ls -la {} \; | tee -a "$DIAGNOSTIC_LOG"
    
    log_diagnostic "System backup completed: $BACKUP_TIME"
}

# Check Claude Code installation and version
check_claude_installation() {
    log_diagnostic "Checking Claude Code installation..."
    
    # Check for claude command
    if command -v claude >/dev/null 2>&1; then
        local claude_version=$(claude --version 2>&1 || echo "Version check failed")
        log_diagnostic "Claude version: $claude_version"
    else
        log_diagnostic "ERROR: Claude command not found in PATH"
    fi
    
    # Check for Node.js Claude Code installation
    if [[ -f "/usr/local/bin/claude" ]] || [[ -f "/usr/bin/claude" ]]; then
        log_diagnostic "Claude binary found"
    else
        log_diagnostic "WARNING: Claude binary not in standard locations"
    fi
    
    # Check Claude Code configuration directories
    for dir in "$CLAUDE_HOME" "$CLAUDE_HOME/hooks" "$CLAUDE_HOME/settings.json"; do
        if [[ -e "$dir" ]]; then
            log_diagnostic "Found: $dir"
        else
            log_diagnostic "MISSING: $dir"
        fi
    done
}

# Enable Claude Code debug logging
enable_claude_debug() {
    log_diagnostic "Enabling Claude Code debug mode..."
    
    # Set debug environment variables
    export CLAUDE_DEBUG=1
    export DEBUG=claude*
    export NODE_ENV=development
    
    # Check for Claude Code debug flags
    log_diagnostic "Debug environment variables set:"
    env | grep -E "(CLAUDE|DEBUG|NODE)" | tee -a "$DIAGNOSTIC_LOG"
    
    # Create debug log monitoring
    mkdir -p "$CLAUDE_HOME/monitoring/logs"
    
    # Start real-time log monitoring
    log_diagnostic "Starting real-time log monitoring..."
    
    # Monitor multiple log sources
    local log_sources=(
        "$CLAUDE_HOME/hooks/logs/*.log"
        "$CLAUDE_HOME/fabrication-prevention/logs/*.log"
        "/tmp/*.log"
        "/var/log/claude/*.log"
    )
    
    for pattern in "${log_sources[@]}"; do
        if compgen -G "$pattern" > /dev/null; then
            log_diagnostic "Monitoring log pattern: $pattern"
        fi
    done
}

# Test hook system registration
test_hook_registration() {
    log_diagnostic "Testing hook system registration..."
    
    # Check settings.json structure
    if [[ -f "$CLAUDE_HOME/settings.json" ]]; then
        log_diagnostic "Settings.json structure:"
        jq '.hooks' "$CLAUDE_HOME/settings.json" 2>/dev/null | tee -a "$DIAGNOSTIC_LOG" || {
            log_diagnostic "ERROR: Invalid JSON in settings.json"
            cat "$CLAUDE_HOME/settings.json" | tee -a "$DIAGNOSTIC_LOG"
        }
        
        # Check specific hook registrations
        local userpromptsubmit_hooks=$(jq -r '.hooks.userPromptSubmit[]?' "$CLAUDE_HOME/settings.json" 2>/dev/null || echo "NONE")
        log_diagnostic "UserPromptSubmit hooks registered: $userpromptsubmit_hooks"
        
        local stop_hooks=$(jq -r '.hooks.stop[]?' "$CLAUDE_HOME/settings.json" 2>/dev/null || echo "NONE")
        log_diagnostic "Stop hooks registered: $stop_hooks"
    else
        log_diagnostic "ERROR: settings.json not found"
    fi
    
    # Check hook file existence and permissions
    log_diagnostic "Checking hook files:"
    for hook in fabrication-detector.sh valued-team-member.sh; do
        local hook_path="$CLAUDE_HOME/hooks/$hook"
        if [[ -f "$hook_path" ]]; then
            local perms=$(ls -la "$hook_path")
            log_diagnostic "Hook $hook: $perms"
            
            # Test basic execution
            if bash -n "$hook_path"; then
                log_diagnostic "Hook $hook: Syntax OK"
            else
                log_diagnostic "ERROR: Hook $hook has syntax errors"
            fi
        else
            log_diagnostic "ERROR: Hook $hook not found"
        fi
    done
}

# Monitor Claude Code process
monitor_claude_process() {
    log_diagnostic "Monitoring Claude Code process..."
    
    # Check for running Claude processes
    local claude_processes=$(ps aux | grep -E "[c]laude" || echo "No Claude processes found")
    log_diagnostic "Claude processes: $claude_processes"
    
    # Check for Node.js processes that might be Claude
    local node_processes=$(ps aux | grep -E "[n]ode.*claude" || echo "No Node Claude processes found")
    log_diagnostic "Node Claude processes: $node_processes"
    
    # Monitor file descriptors and ports
    if command -v lsof >/dev/null 2>&1; then
        local claude_fds=$(lsof | grep claude 2>/dev/null | head -5 || echo "No Claude file descriptors found")
        log_diagnostic "Claude file descriptors: $claude_fds"
    fi
}

# Set up real-time hook execution monitoring
setup_hook_monitoring() {
    log_diagnostic "Setting up real-time hook execution monitoring..."
    
    # Create hook execution test logger
    cat > "$CLAUDE_HOME/hooks/hook-execution-monitor.sh" << 'EOF'
#!/bin/bash
# Hook Execution Monitor for Diagnostics
# This script logs whenever it's executed to verify hook system functionality

MONITOR_LOG="/home/<USER>/.claude/monitoring/logs/hook-execution-monitor.log"
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')

echo "[$TIMESTAMP] HOOK_EXECUTION_MONITOR: UserPromptSubmit hook executed with args: $*" >> "$MONITOR_LOG"
echo "[$TIMESTAMP] HOOK_EXECUTION_MONITOR: STDIN available: $(test -p /dev/stdin && echo 'YES' || echo 'NO')" >> "$MONITOR_LOG"
echo "[$TIMESTAMP] HOOK_EXECUTION_MONITOR: Environment: PWD=$PWD, USER=$USER" >> "$MONITOR_LOG"

# Try to read stdin if available
if [[ -p /dev/stdin ]]; then
    local stdin_content=$(timeout 1 cat /dev/stdin 2>/dev/null || echo "TIMEOUT")
    echo "[$TIMESTAMP] HOOK_EXECUTION_MONITOR: STDIN content: $stdin_content" >> "$MONITOR_LOG"
fi

# Always return success to not block Claude
exit 0
EOF
    
    chmod +x "$CLAUDE_HOME/hooks/hook-execution-monitor.sh"
    log_diagnostic "Created hook execution monitor"
    
    # Temporarily add monitor to settings.json for testing
    local temp_settings=$(mktemp)
    jq '.hooks.userPromptSubmit += ["hook-execution-monitor.sh"]' "$CLAUDE_HOME/settings.json" > "$temp_settings"
    mv "$temp_settings" "$CLAUDE_HOME/settings.json"
    log_diagnostic "Added hook execution monitor to settings.json"
}

# Generate comprehensive system report
generate_system_report() {
    log_diagnostic "Generating comprehensive system report..."
    
    local report_file="$CLAUDE_HOME/monitoring/logs/system-report-$BACKUP_TIME.txt"
    
    cat > "$report_file" << EOF
Claude Code Hook Execution Failure Diagnostic Report
Generated: $(get_timestamp)
Backup ID: $BACKUP_TIME

=== SYSTEM INFORMATION ===
$(uname -a)
$(lsb_release -a 2>/dev/null || echo "LSB info not available")

=== CLAUDE INSTALLATION ===
$(check_claude_installation 2>&1)

=== HOOK CONFIGURATION ===
$(cat "$CLAUDE_HOME/settings.json" 2>/dev/null || echo "settings.json not readable")

=== HOOK FILES ===
$(find "$CLAUDE_HOME/hooks" -name "*.sh" -exec ls -la {} \; 2>/dev/null)

=== RECENT LOGS ===
$(tail -20 "$CLAUDE_HOME/hooks/logs/fabrication-incidents.log" 2>/dev/null || echo "No fabrication incidents log")

=== PROCESS INFORMATION ===
$(ps aux | grep -E "[c]laude|[n]ode" || echo "No relevant processes found")

=== NETWORK CONNECTIONS ===
$(netstat -tlnp 2>/dev/null | grep -E ":9090|:3000|:8080" || echo "No monitoring ports found")

=== ENVIRONMENT VARIABLES ===
$(env | grep -E "(CLAUDE|DEBUG|NODE)" || echo "No relevant environment variables")

EOF
    
    log_diagnostic "System report generated: $report_file"
}

# Main execution
main() {
    log_diagnostic "Starting Claude Code diagnostics setup..."
    
    create_system_backup
    check_claude_installation
    enable_claude_debug
    test_hook_registration
    monitor_claude_process
    setup_hook_monitoring
    generate_system_report
    
    log_diagnostic "Claude Code diagnostics setup completed"
    log_diagnostic "Monitor logs at: $DIAGNOSTIC_LOG"
    log_diagnostic "Next step: Start monitoring infrastructure"
}

# Run if executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi