{"dashboard": {"id": null, "title": "Claude Code Hook Execution Monitoring", "tags": ["claude-code", "hooks", "fabrication-detection"], "timezone": "browser", "panels": [{"id": 1, "title": "Hook Execution Timeline", "type": "graph", "targets": [{"expr": "rate(claude_hook_executions_total[1m])", "legendFormat": "{{type}} - {{status}}"}], "yAxes": [{"label": "Executions per minute"}], "xAxes": [{"type": "time"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "UserPromptSubmit Hook Status", "type": "stat", "targets": [{"expr": "claude_hook_executions_total{type=\"UserPromptSubmit\"}", "legendFormat": "Total Executions"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 1}, {"color": "green", "value": 5}]}}}, "gridPos": {"h": 4, "w": 6, "x": 12, "y": 0}}, {"id": 3, "title": "Fabrication Detection Scores", "type": "graph", "targets": [{"expr": "claude_fabrication_detection_score", "legendFormat": "{{pattern_type}} - {{severity}}"}], "yAxes": [{"label": "Fabrication Score (0-100)", "min": 0, "max": 100}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "Hook Registration Status", "type": "table", "targets": [{"expr": "claude_hook_registration_status", "format": "table"}], "gridPos": {"h": 8, "w": 6, "x": 12, "y": 4}}, {"id": 5, "title": "Fabrication Bypass Detection", "type": "graph", "targets": [{"expr": "claude_fabrication_bypass_total", "legendFormat": "{{severity}} - {{detected}}"}], "yAxes": [{"label": "Bypass Count"}], "alert": {"conditions": [{"evaluator": {"params": [1], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "5m", "now"]}, "reducer": {"params": [], "type": "last"}, "type": "query"}], "executionErrorState": "alerting", "for": "5s", "frequency": "1s", "handler": 1, "name": "Fabrication Bypass Alert", "noDataState": "no_data", "notifications": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 6, "title": "System Health Overview", "type": "stat", "targets": [{"expr": "up", "legendFormat": "System Status"}], "gridPos": {"h": 4, "w": 6, "x": 12, "y": 12}}], "time": {"from": "now-5m", "to": "now"}, "refresh": "1s", "schemaVersion": 16, "version": 0}}