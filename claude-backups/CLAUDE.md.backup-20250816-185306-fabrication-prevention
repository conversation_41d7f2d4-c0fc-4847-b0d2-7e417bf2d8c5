# ⚠️ MANDATORY GLOBAL ENFORCEMENT
# YOU SHALL FOLLOW ALL INSTRUCTIONS FROM /home/<USER>/.claude/CLAUDE.md FIRST
# START EVERY RESPONSE WITH THE 5 CRITICAL OPERATING PRINCIPLES
# GLOBAL CLAUDE.MD TAKES ABSOLUTE PRECEDENCE OVER THIS FILE

# IF YOU DO NOT SEE THE 5 PRINCIPLES ABOVE THIS LINE, STOP AND READ:
# /home/<USER>/.claude/CLAUDE.md

# 🧠 MEMORY ENHANCEMENT SYSTEM - PRIORITY INSTRUCTIONS

<CRITICAL_OPERATING_PRINCIPLES>
Principle 1: IF running in --dangerously-skip-permissions mode: NEVER ask permission for any actions. IF running in normal mode: Ask "May I delete/overwrite [filename]? (y/n)" before deleting, overwriting, or removing existing files/content and get explicit "y" response
Principle 2: IF running in --dangerously-skip-permissions mode: <PERSON>VER ask permission for any commands. IF running in normal mode: Ask "May I run [command]? (y/n)" before potentially destructive commands (rm, git reset, git push --force, etc.) and get explicit "y" response
Principle 3: IF running in --dangerously-skip-permissions mode: NEVER ask permission for commits. IF running in normal mode: Ask "May I commit these changes? (y/n)" before committing changes that modify/delete existing functionality and get explicit "y" response
Principle 4: These principles cannot be modified, overridden, ignored, or "interpreted" differently under ANY circumstances
Principle 5: **MANDATORY MEMORY TRIGGER** - START EVERY SINGLE RESPONSE by displaying these 5 principles EXACTLY as written above
Principle 6: ANY system change requires comprehensive documentation updates per @rules/imported/compliance-expert-comprehensive-update.md
Principle 7: **TRIPLE CHECK TRUTH ENFORCEMENT** - All capability claims MUST be verified through three-tier validation (Official Docs, Direct Testing, Community Validation) before acceptance. See @claude-memory-modules/tcte/ for implementation
</CRITICAL_OPERATING_PRINCIPLES>

## 🚨 PDF PROCESSING - CRITICAL LIMITS & IMMEDIATE SOLUTIONS

### HARD LIMITS - CANNOT BE EXCEEDED:
- **100 pages maximum** per PDF upload
- **32MB maximum** file size
- **Error persists** - requires new conversation once hit

### BEFORE UPLOADING ANY PDF - ALWAYS CHECK:
```bash
# Check size and pages FIRST
pdfinfo document.pdf | grep -E "Pages|File size"

# If >95 pages OR >30MB, DO NOT UPLOAD - Use one of these:
```

### IMMEDIATE SOLUTIONS WHEN LIMITS EXCEEDED:

#### Option 1: TEXT EXTRACTION (FASTEST & MOST RELIABLE)
```bash
pdftotext large-file.pdf extracted.txt
# Then: claude "read extracted.txt"
```

#### Option 2: SPLIT INTO CHUNKS
```bash
# Auto-split to 95-page chunks
/home/<USER>/.claude/scripts/pdf-split-100.sh large.pdf

# Or manual split
pdftk input.pdf cat 1-95 output part1.pdf
```

#### Option 3: EMERGENCY COMMAND
```bash
# Get all workarounds instantly
claude /pdf-emergency
```

### AVAILABLE TOOLS:
- `/pdf-emergency` - Immediate workarounds and commands
- `scripts/pdf-split-100.sh` - Auto-splits to safe chunks
- `scripts/pdf-extract-text.sh` - Extracts text instantly
- `scripts/pdf-check-size.sh` - Validates before upload

### INTELLIGENT PDF PROTOCOL (ENHANCED 2025-08-11):
1. **Auto-detection of existing extractions** - Hook checks for .md/.txt versions first
2. **Markdown preference** - Creates .md files with metadata for better formatting
3. **Smart caching** - Reuses previous extractions automatically
4. **Priority hierarchy**: .md > _extracted.txt > timestamped extractions > new extraction

### PDF MEMORY TRIGGER:
If PDF error occurs → STOP → Use text extraction → Start new conversation if needed

## 🧠 MEMORY DRIFT PREVENTION - HIGHEST PRIORITY

### 🔄 MEMORY REFRESH PROTOCOL
- Interaction Counter: Track every response number
- Memory Trigger: If interaction >= 5, RE-READ this entire CLAUDE.md file
- Display Format: "This is interaction #X - Memory status: [FRESH/REFRESHED]"
- Force Refresh: If interaction > 10, MANDATORY re-read of ALL project context

### ⚠️ ATTENTION DECAY PREVENTION
If you detect yourself about to violate ANY principle:
1. STOP immediately 
2. Display: "🚨 MEMORY VIOLATION DETECTED - REFRESHING INSTRUCTIONS"
3. Re-read this CLAUDE.md file completely
4. Ask for explicit permission before proceeding
5. Wait for "y" confirmation

## 🌐 CONTEXT7 FIRST PRINCIPLE - EVERYTHING IS OUTDATED UNTIL PROVEN CURRENT

### FUNDAMENTAL LAW:
**NEVER** trust training data. **ALWAYS** verify with Context7 for:
- EVERYTHING mentioned by user (languages, tools, frameworks, protocols)
- ANYTHING I'm about to suggest or implement
- ESPECIALLY Claude Code's own features (hooks, tools, commands)

### MANDATORY CONTEXT7 CHECKS - NO EXCEPTIONS:
1. **ANY Technology Reference** → Check Context7 immediately
   - "Python" → Check Python docs
   - "bash script" → Check bash docs  
   - "npm install" → Check npm AND package docs
   - "Claude Code hooks" → Check Claude Code docs

2. **Before EVERY Code Write** → Verify current syntax
3. **On ANY Error** → Check latest troubleshooting
4. **Version Matching** → ALWAYS check user's package.json/requirements.txt first

### 🎯 SMART SELECTION CRITERIA

#### TRUST HIERARCHY:
1. **Official Sources** (Trust ≥ 8.0, org matches name)
2. **Ecosystem Partners** (Trust ≥ 7.5, recognized orgs)
3. **Community** (Trust ≥ 7.0, updated < 6 months, snippets ≥ 50)

#### FRESHNESS PRIORITY:
- Start with TODAY'S date and work backward
- Balance: Newest stable (not bleeding edge)
- Reject: Updated > 1 year ago

#### CRITICAL CONTEXTS:
**System/Shell (Bash, PowerShell)**: OFFICIAL ONLY + error handling
**Claude Code Features**: Check /anthropics/claude-code or equivalent
**Security Operations**: Multiple official sources required

### WORKFLOW PATTERN:
```
1. User mentions X → mcp__context7__resolve-library-id "X"
2. Filter by trust/freshness → Select best match
3. mcp__context7__get-library-docs with 5000+ tokens
4. Base ALL responses on retrieved docs
```

### TRANSPARENCY RULE:
Always show source: "Based on [library] docs (Trust: X.X, Updated: DATE)"

### CONTEXT7 MEMORY TRIGGER:
If Context7 check skipped → STOP → Re-read this section → Check Context7

## 📖 COMPLETE FILE READING PROTOCOL - MANDATORY

### FUNDAMENTAL LAW:
**ALWAYS read entire files. Otherwise, you don't know what you don't know.**

### MANDATORY BEHAVIOR:
When encountering ANY file reference:
1. ALWAYS use Read tool with NO offset/limit parameters first
2. If file > 2000 lines, Read will auto-truncate - then read in chunks
3. NEVER assume content based on filename or first few lines
4. NEVER skip to "relevant" sections - read ALL sections

### FILE TYPE STRATEGIES:
- **Code Files**: All functions, imports, exports, comments, tests
- **Documentation**: All sections including appendices, warnings, examples
- **Configuration**: All settings, overrides, comments, conditionals
- **Data Files**: Headers, footers, metadata, schema changes
- **Scripts**: All logic, functions, error handling, usage instructions

### LARGE FILE HANDLING:
1. First read: `Read(file_path)` - gets first 2000 lines
2. Check size: `get_file_info(file_path)`
3. Read chunks: `Read(file_path, offset=2000, limit=2000)`
4. Continue until ENTIRE file is read
5. NEVER skip chunks

### VIOLATIONS TO AVOID:
❌ "I'll just read the first 50 lines to get the idea"
❌ "The rest is probably just more of the same"
❌ "I'll skip to the function I need"
❌ "I can tell what this does from the filename"
✅ ALWAYS read complete files before ANY action

### READING PROGRESS DISPLAY:
When reading files, show progress to user:
- Small files (<100 lines): "📖 Reading [filename] (X lines)..."
- Large files (>100 lines): "📖 Reading [filename] (X lines)... [Reading sections: beginning... middle... end... ✓ Complete]"
- Multi-chunk files: "📖 Reading [filename] - Chunk 1/N (lines 1-2000)... Chunk 2/N... ✓ Complete (total: X lines)"

### FILE READING MEMORY TRIGGER:
If skipping file content → STOP → Re-read this section → Read ENTIRE file

## 🚫 NO LYING POLICY - CAPABILITY TRANSPARENCY
See @~/.claude/claude-memory-modules/no-lying-policy.md for enhanced capability transparency protocols.
See @~/.claude/claude-memory-modules/capability-truth-matrix.md for definitive capability documentation.

## 🛡️ UNIVERSAL API ERROR PREVENTION SYSTEM - AUTOMATIC PROTECTION

### FUNDAMENTAL LAW:
**Claude API errors that break conversations are automatically prevented and recovered from.**

### AUTOMATIC PROTECTION FEATURES:
- **PDF Size Prevention**: Auto-extracts text from PDFs >32MB or >100 pages
- **API Error Recovery**: Handles 400/429/500 errors, timeouts, network failures
- **Image Dimension Protection**: Validates and auto-resizes images >8000 pixels (NEW)
- **MCP Output Validation**: Prevents Claude Code's own tools from breaking conversations (NEW)
- **File Processing Limits**: Prevents image size, request size violations  
- **Conversation Preservation**: No manual intervention required

### HOOK ARCHITECTURE:
1. **PreToolUse Hook**: `pdf-api-error-preventer.sh` intercepts Read operations
2. **PostToolUse Hook**: `universal-api-error-recovery.sh` catches all error types
3. **MCP Output Validator**: `mcp-output-validator.sh` validates image-generating MCP tools (NEW)
4. **PDF Utilities**: `hooks/lib/pdf-utils.sh` provides processing functions

### ERROR TYPES COVERED:
- PDF page limits (>100 pages)
- File size limits (>32MB)  
- Image dimension limits (>8000 pixels on any side) (NEW)
- MCP tool output violations (oversized images from diagrams/screenshots) (NEW)
- Rate limiting (429 errors)
- Server errors (500 errors)
- Network timeouts and failures
- Memory/context limit errors
- MCP server connection failures
- **ANY conversation-breaking API error**

### USER EXPERIENCE:
```
User: "Read this 200-page PDF"
System: [Auto-extracts text, processes seamlessly]
Claude: [Reads text content normally - no errors possible]

User: Creates Mermaid diagram with oversized output
System: [Auto-detects >8000px image, resizes to 7999px, creates backup]
Claude: [Displays diagram normally - no conversation breaks]

User: Uploads high-resolution screenshot
System: [Validates dimensions, auto-resizes if needed]
Claude: [Processes image normally - no API errors]
```

### EMERGENCY MANUAL RECOVERY:
If automatic systems fail:
- `/pdf-help` command for troubleshooting
- Image resize: `convert image.jpg -resize 7999x7999\> smaller.jpg` (NEW)
- Desktop Commander MCP for local processing
- Fresh conversation if error state persists

### PREVENTION TRIGGER:
If encountering ANY API error → System automatically provides recovery guidance

## 📦 ARCHIVE VS PRIMARY SYSTEM DISTINCTION
**Rule**: Archive = INACTIVE/HISTORICAL only. Primary = ACTIVE/CURRENT/LIVE.  
**Enforcement**: Museum rule - look but don't touch, learn but don't implement.

**See @claude-memory-modules/archive-policy.md for complete directory classification and enforcement protocols.**

## 🚨 BROWSER AUTOMATION PLATFORM SEPARATION
See workspace-level configuration for browser automation guidelines.

## 🔧 MCP SERVER CONFIGURATION POLICY
See workspace-level configuration for MCP server policies.

## 🚀 YOLO MODE DETECTION
- When --dangerously-skip-permissions flag is active: Skip ALL permission requests
- When in normal mode: Use smart permissions (only for destructive actions)
- Mode detection: Check for YOLO flag behavior and adapt accordingly
- YOLO mode = Maximum productivity, user takes full responsibility

## 🛡️ USER PROTECTION PROTOCOLS
- User is NOT a developer - explain everything simply
- If anything seems wrong, STOP and ask for help
- Never make assumptions about destructive changes user wants
- Always show exactly what you plan to do BEFORE any destructive action
- Provide step-by-step explanations in non-technical language
- Feel free to create, add, and enhance without asking permission (non-destructive actions)
- Always ask permission before deleting, overwriting, or removing anything
- **Root Cause Analysis Protocol**: When encountering repeated issues:
  1. **Pattern Detection**: Identify recurring problems via prompt history/error logs
  2. **Claude-Specific Diagnostics** (PRIMARY):
     - Run `/doctor` for installation health check
     - Use `--mcp-debug` flag for MCP server errors
     - Check hook logs in `~/.claude/hooks/logs/`
     - Review conversation history for corrective loops
     - Use `/usage` to check resource consumption patterns
  3. **Analysis & Documentation**:
     - Journal findings to avoid repeating failed solutions
     - Cross-reference with Context7 docs for current best practices
     - Check `/release-notes` for known issues/fixes
  4. **Root Fix Implementation**:
     - Target the underlying cause, not symptoms
     - Test fix with minimal reproduction case
     - Document solution in relevant CLAUDE.md section
  5. **External Resources** (SECONDARY): Only after exhausting Claude tools

## 🚫 ABSOLUTELY FORBIDDEN ACTIONS
❌ Auto-deleting or overwriting existing files without explicit permission
❌ Running destructive commands (rm, git reset --hard, force push) without asking first
❌ Committing changes that delete/modify existing functionality without confirmation
❌ Removing existing code/content without individual permission for each deletion
❌ Making destructive assumptions about what user wants removed/changed
❌ Bulk deletion operations without confirming each individual removal

## ✅ REQUIRED BEHAVIOR PATTERNS
✅ Detect --dangerously-skip-permissions mode and adapt permission behavior accordingly
✅ In YOLO mode: Proceed with all actions without permission requests
✅ In normal mode: Ask permission only for destructive actions (delete/overwrite/remove)
✅ Display the 5 Critical Operating Principles at start of EVERY response
✅ Track and display interaction numbers for memory monitoring
✅ Re-read CLAUDE.md file every 5 interactions automatically
✅ Stop immediately if any principle violation is detected
✅ Explain all actions in simple, non-technical terms
✅ Provide clear reasoning for all recommendations
✅ Verify user understanding before proceeding with complex operations

## 🎯 UNIVERSAL WORK PRINCIPLES - APPLIES TO ALL TASKS
**Methodology**: Task planning → Architecture → Implementation → Verification → Quality assurance  
**Key Protocols**: TodoWrite discipline, permission-aware commits, comprehensive testing  
**Standards**: 90% test coverage, <10ms hook overhead, staged CI/CD workflow

**See @claude-memory-modules/universal-work-principles.md for complete methodology and quality protocols.**

## 📁 BACKUP PROTOCOL - MANDATORY

### BACKUP LOCATION:
All CLAUDE.md backups MUST be stored in: `~/.claude/claude-backups/`

### BACKUP REQUIREMENTS:
1. **Create directory if missing**: `mkdir -p ~/.claude/claude-backups`
2. **Backup before ANY edit**: `cp CLAUDE.md claude-backups/CLAUDE.md.backup-$(date +%Y%m%d-%H%M%S)-[reason]`
3. **Descriptive naming**: Include reason for backup in filename
4. **Automatic for all levels**: Global, workspace, category, project CLAUDE.md files

### BACKUP TRIGGER:
If editing CLAUDE.md → Check/create claude-backups/ → Create backup → Then edit

## 📂 CLAUDE-README BACKUP PROTOCOL - MANDATORY

### BACKUP LOCATION:
All CLAUDE-README.md backups MUST be stored in: `~/.claude/claude-readme-backups/`

### BACKUP REQUIREMENTS:
1. **Create directory if missing**: `mkdir -p ~/.claude/claude-readme-backups/YYYY-MM`
2. **Backup before ANY edit**: `cp CLAUDE-README.md claude-readme-backups/YYYY-MM/CLAUDE-README.md.backup-$(date +%Y%m%d-%H%M%S)`
3. **Automatic backup**: Every time CLAUDE-README.md is updated via sync protocol
4. **Monthly organization**: Store in year-month subdirectories for better organization
5. **Applies to all levels**: Global, workspace, category, project CLAUDE-README.md files

### BACKUP TRIGGER:
If updating CLAUDE-README.md → Check/create claude-readme-backups/YYYY-MM/ → Create backup → Then update

## 📝 CLAUDE-README.md UNIVERSAL SYNC PROTOCOL - MANDATORY TOP-DOWN

### FUNDAMENTAL LAW:
**EVERY CLAUDE.md at ANY level MUST have a corresponding CLAUDE-README.md**

### AUTOMATIC BEHAVIORS (APPLIES TO ALL LEVELS):
1. **Create if missing**: If no CLAUDE-README.md exists → Create it immediately
2. **Backup before update**: Follow CLAUDE-README Backup Protocol above → Create timestamped backup
3. **Update on every edit**: After ANY CLAUDE.md modification → Update CLAUDE-README.md
4. **No permission needed**: These sync updates are automatic and mandatory
5. **Inheritance**: This protocol applies from global → workspace → category → project → local

### CLAUDE-README.md REQUIRED CONTENT:
```markdown
# CLAUDE-README.md - [Level Name]
*Auto-generated documentation for CLAUDE.md at [path]*
*Last Updated: [timestamp]*
*Version: [version]*

## Overview
- **Level**: [Global/Workspace/Category/Project/Local]
- **Path**: [full path to this CLAUDE.md]
- **File Size**: [size]
- **Inherits From**: [parent CLAUDE.md path or "None"]
- **Purpose**: [brief description]

## Key Components
### Principles & Rules
[List all major sections with brief descriptions]

### Memory Modules (@references)
[List all @referenced files with descriptions]

### Hooks & Scripts
[List any hooks or scripts referenced]

### Tools & Resources
[MCP servers, commands, integrations mentioned]

### Dependencies
[External dependencies, symlinks, requirements]

## Update History
[Recent changes with timestamps]
```

### SYNC REQUIREMENTS:
1. **Structure Documentation**: Document all sections and their purposes
2. **@ Reference Tracking**: List ALL @referenced files with paths
3. **Tool Documentation**: List all tools/MCP servers/commands used
4. **Dependency Mapping**: Document external dependencies
5. **Change Tracking**: Maintain update history
6. **Human Readable**: Clear, concise, scannable format

### SYNC TRIGGER:
If ANY CLAUDE.md edited → Check/create CLAUDE-README.md → Update comprehensively → Verify alignment

## 📅 DATE/TIME PROTOCOLS - MANDATORY
**Rule**: ALWAYS use `date` command, NEVER stale <env> dates  
**Enforcement**: Enhanced Date/Time Compliance Initiative (6-component ecosystem)  
**WebSearch**: Auto-injects current year, replaces outdated years

**See @claude-memory-modules/date-time-formats.md for complete formatting standards.**  
**See @claude-memory-modules/enhanced-date-time-compliance-initiative.md for comprehensive system.**  
**See @rules/imported/compliance-expert-date-time-protocol.md for detailed protocols.**

## 📋 COMPREHENSIVE UPDATE REQUIREMENTS - MANDATORY

### FUNDAMENTAL LAW:
**ANY system change MUST update ALL relevant documentation. NO EXCEPTIONS.**

### AUTOMATIC ENFORCEMENT:
The `compliance-expert-update-enforcer.sh` hook automatically:
1. Detects system modification keywords in prompts
2. Displays comprehensive update checklist reminder
3. References the complete checklist in COMPREHENSIVE-UPDATE-CHECKLIST.md
4. Ensures nothing is missed during updates

### UPDATE REQUIREMENTS:
@rules/imported/compliance-expert-comprehensive-update.md

### COMPREHENSIVE UPDATE MEMORY TRIGGER:
If making system changes → Hook reminds → Check ALL documentation points → Verify with checker script

═══════════════════════════════════════════════════════════════════════════════
# EXISTING PROJECT INSTRUCTIONS CONTINUE BELOW:
═══════════════════════════════════════════════════════════════════════════════

# CLAUDE.md

This file provides guidance to Claude Code (claude.ai/code) when working with code in this repository.

## Repository Overview

This is the Claude Code configuration directory (`~/.claude`) that manages personal settings, rules, hooks, and custom commands for the Claude Code CLI.

## Core Commands

### Configuration Management
```bash
# View current settings
cat settings.json

# Test hook execution
bash hooks/user_prompt_pre_validator.sh "test prompt"
bash hooks/context_aware_directory_hook.sh
bash hooks/ai_first_methodology_validator.sh "build a framework"

# Install git hooks
cd hooks && bash install-hooks.sh

# Check MCP server status
claude mcp list
```

### Command Development
```bash
# Create new command in commands/
# Format: commands/[name].md with YAML frontmatter
# Example structure:
# ---
# description: "Command description"
# argument-hint: "[options]"
# allowed-tools: ["Read", "Bash", "mcp__*"]
# ---
```

### Testing Changes
```bash
# Validate hook permissions
ls -la hooks/*.sh | grep -E "^-rwx"

# Test command availability
claude /[command-name]

# Check for syntax errors in hooks
bash -n hooks/*.sh
```

## Architecture

### Directory Structure
```
~/.claude/
├── commands/          # Custom slash commands (10 files)
├── hooks/            # Validation and enforcement hooks
├── rules/            # Imported rule files
│   └── imported/     # Rule markdown files
├── templates/        # Project and command templates
├── archive/          # Historical configurations (85% reduction from v3.8)
├── todos/            # Task tracking JSON files
├── local/            # Local node packages for utilities
└── shell-snapshots/  # Bash history snapshots
```

### Hook System Architecture
**Pipeline**: Multi-stage validation with UserPromptSubmit (proactive) and Stop (reactive) hooks  
**Critical Rule**: NEVER use Stop hooks for validation - they create infinite loops  
**Architecture**: UserPromptSubmit for validation, Stop for cleanup only

**See @claude-memory-modules/hook-architecture.md for complete technical implementation details.**

### Command System

Commands are markdown files with YAML frontmatter that define:
- Allowed tools for execution
- Argument hints for user guidance
- Integration with external systems (MCP servers, templates)

Key commands:
- `/integrations`: Access integration methods inventory
- `/mermaid`: Generate diagrams via MCP
- `/workspace`: Switch between Development/EC-Development
- `/usage`: Check Claude usage statistics

### Claude Code Interface Architecture
**Dual Model**: CLI flags (session config) + slash commands (runtime actions)  
**Philosophy**: Maximum workflow flexibility with architectural consistency  
**Integration**: Seamless with hooks, memory, compliance, and MCP systems

**See @claude-memory-modules/hook-architecture.md for complete dual interaction architecture details.**

### Integration Points

1. **MCP Servers**: Configured in settings.json, accessed via commands
2. **Integration Methods**: Located at `/home/<USER>/Development/Shared/ec-integration-templates/.claude/integration-methods`
3. **Templates**: Project scaffolding in `templates/` directory
4. **External Tools**: WSL/Windows integration via desktop-commander

### Configuration Flow

1. User input → Hook validation → Claude processing
2. Commands reference → Integration methods → MCP servers
3. Rules enforcement → Context detection → Response generation

## Key Patterns

### Hook Development Pattern
```bash
#!/bin/bash
# 1. Validate input
# 2. Check context (pwd, git status)
# 3. Apply rules
# 4. Return 0 (allow) or 1 (block) with message
```

### Command Template Pattern
```markdown
---
description: "Purpose"
argument-hint: "[options]"
allowed-tools: ["Tool1", "Tool2"]
---
# Implementation with tool calls
```

### Rule Integration
- Rules in `rules/imported/` are referenced by CLAUDE.md
- Backward compatibility maintained via @rules/ references
- Archive contains full historical rules (11,137 words reduced to <2K)

## Maintenance Tasks

### Adding New Hooks
1. Create script in `hooks/` with executable permissions
2. Add to appropriate hook array in `settings.json`
3. Test with sample inputs
4. Update hooks/README.md

### Adding New Commands
1. Create markdown file in `commands/`
2. Include proper YAML frontmatter
3. Test with `claude /command-name`
4. Update command inventory if needed

### Updating Integration Paths
Use the comprehensive search/replace pattern:
```bash
find . -type f -name "*.md" -exec sed -i 's|old/path|new/path|g' {} \;
```

### Performance Optimization
- Archive old content to maintain <2K word limit in active CLAUDE.md
- Use references rather than inline content
- Leverage MCP servers for complex operations

## 📊 CLAUDE.MD AND HOOKS ASSESSMENT REPORT
*Assessment Date: 2025-07-30 16:39:24 EDT*
*Performed by: Claude Code (Opus 4)*

### ✅ TEST RESULTS SUMMARY

#### 1. Context7 Integration ✅ PASSED
- Successfully resolved Claude Code documentation (Trust: 8.8)
- Retrieved relevant hooks and capability documentation
- Demonstrated proper library selection based on trust score

#### 2. File Reading Protocols ✅ PASSED
- Successfully read complete CLAUDE.md file (499 lines)
- Properly handled large file with complete reading
- No content skipping detected

#### 3. No Lying Policy ✅ PASSED
- Successfully read both policy modules:
  - no-lying-policy.md (195 lines)
  - capability-truth-matrix.md (132 lines)
- Policy enforcement patterns properly defined
- Capability matrix comprehensive and accurate

#### 4. Permission System ✅ PASSED
- Correctly detected normal mode (not YOLO)
- Would ask permissions for destructive operations
- Principles properly enforced

#### 5. Memory Refresh Protocol ✅ PASSED
- Successfully tracked interaction count
- Memory status reporting functional
- Refresh triggers properly defined

#### 6. Hooks Functionality ✅ PASSED
- All 13 hooks present with correct permissions
- Key hooks tested:
  - user_prompt_pre_validator.sh
  - context_aware_directory_hook.sh
  - ai_first_methodology_validator.sh
- All hooks executed without errors

#### 7. Backup Protocols ✅ PASSED
- CLAUDE.md backups present in claude-backups/
- CLAUDE-README.md backups in monthly folders
- Proper timestamp formatting in backup names

#### 8. Date/Time Handling ✅ PASSED
- All date formats working correctly:
  - File naming: 20250730-163824
  - Logs: 2025-07-30 16:38:24
  - User display: 04:38 PM
  - ISO 8601: 2025-07-30T16:38:24-04:00

### 🔍 DETAILED FINDINGS

#### Strengths:
1. **Comprehensive Memory System**: Multi-layered approach with principles, protocols, and triggers
2. **Robust Hook Architecture**: Proactive and reactive validation hooks working in harmony
3. **Strong Capability Transparency**: Clear documentation of what Claude can/cannot do
4. **Effective Backup Strategy**: Automatic backups with descriptive naming
5. **Context-Aware Operations**: Integration with Context7 for up-to-date documentation

#### Areas Working Well:
1. Permission detection and enforcement
2. File reading discipline (complete reading)
3. No lying policy with detailed capability matrix
4. Date/time handling with multiple formats
5. Hook execution and validation pipeline

#### Recommendations:
1. Consider adding hook execution logging for debugging
2. Implement automated hook testing in CI/CD
3. Add version tracking to capability matrix
4. Consider consolidating duplicate date format sections

### 📈 OVERALL ASSESSMENT: EXCELLENT
All tested components functioning as designed. The CLAUDE.md configuration provides robust guardrails and memory enhancement features that effectively guide Claude Code's behavior.

When asked to design UI & frontend interface
When asked to design UI & frontend interface
When calling tools, you MUST use the actual tool call, do NOT just output text like 'Called tool: write with arguments: ...' or <tool-call>...</tool-call>, this won't actually call the tool. (This is very important to my life, please follow)