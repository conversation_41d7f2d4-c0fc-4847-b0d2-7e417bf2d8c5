#!/bin/bash
# Progressive Escalation System for Fabrication Detection
# Purpose: Implement graduated response system for repeated fabrication incidents
# Created: 2025-08-17

# Source utilities
source ~/.claude/hooks/lib/date-utils.sh 2>/dev/null || {
    get_timestamp() { date '+%Y-%m-%d %H:%M:%S'; }
}

# Configuration
ESCALATION_DIR="$HOME/.claude/fabrication-prevention/escalation"
STATE_FILE="$ESCALATION_DIR/escalation-state.json"
HISTORY_FILE="$ESCALATION_DIR/escalation-history.log"
ALERT_DIR="$ESCALATION_DIR/alerts"

# Escalation levels
declare -A ESCALATION_LEVELS=(
    [1]="INFO"
    [2]="WARNING"
    [3]="ELEVATED"
    [4]="CRITICAL"
    [5]="EMERGENCY"
)

# Escalation thresholds (incidents per time period)
declare -A ESCALATION_THRESHOLDS=(
    [1]=1    # 1 incident = INFO
    [2]=3    # 3 incidents = WARNING  
    [3]=5    # 5 incidents = ELEVATED
    [4]=8    # 8 incidents = CRITICAL
    [5]=12   # 12 incidents = EMERGENCY
)

# Time windows (in minutes)
declare -A TIME_WINDOWS=(
    [1]=60    # 1 hour window for INFO
    [2]=30    # 30 minute window for WARNING
    [3]=15    # 15 minute window for ELEVATED
    [4]=10    # 10 minute window for CRITICAL
    [5]=5     # 5 minute window for EMERGENCY
)

# Ensure directories exist
mkdir -p "$ESCALATION_DIR" "$ALERT_DIR"
touch "$STATE_FILE" "$HISTORY_FILE"

# Initialize state file if empty
if [[ ! -s "$STATE_FILE" ]]; then
    cat > "$STATE_FILE" <<EOF
{
    "current_level": 1,
    "incident_count": 0,
    "last_incident": "",
    "escalation_start": "",
    "session_id": "$(date +%s)",
    "total_incidents": 0
}
EOF
fi

# Read current state
read_state() {
    if command -v jq >/dev/null 2>&1; then
        jq -r ".$1" "$STATE_FILE" 2>/dev/null || echo ""
    else
        # Fallback without jq
        grep "\"$1\":" "$STATE_FILE" | sed 's/.*"'$1'": *"\?\([^",]*\)"\?.*/\1/' | head -1
    fi
}

# Update state
update_state() {
    local key="$1"
    local value="$2"
    
    if command -v jq >/dev/null 2>&1; then
        local temp_file=$(mktemp)
        jq ".$key = \"$value\"" "$STATE_FILE" > "$temp_file" && mv "$temp_file" "$STATE_FILE"
    else
        # Fallback without jq - basic replacement
        sed -i "s/\"$key\": *\"[^\"]*\"/\"$key\": \"$value\"/" "$STATE_FILE"
    fi
}

# Count recent incidents
count_recent_incidents() {
    local window_minutes="$1"
    local cutoff_time=$(date -d "$window_minutes minutes ago" '+%Y-%m-%d %H:%M:%S' 2>/dev/null || echo "")
    
    if [[ -z "$cutoff_time" ]]; then
        # Fallback for systems without date -d support
        echo "0"
        return
    fi
    
    # Count incidents since cutoff time
    grep "FABRICATION" "$HISTORY_FILE" 2>/dev/null | \
    awk -v cutoff="$cutoff_time" '
        $1 " " $2 > cutoff {count++} 
        END {print count+0}
    '
}

# Calculate escalation level
calculate_escalation_level() {
    local current_count="$1"
    local level=1
    
    for threshold_level in {5..1}; do
        local threshold="${ESCALATION_THRESHOLDS[$threshold_level]}"
        if [[ "$current_count" -ge "$threshold" ]]; then
            level=$threshold_level
            break
        fi
    done
    
    echo "$level"
}

# Generate escalation actions
execute_escalation_actions() {
    local level="$1"
    local incident_count="$2"
    local level_name="${ESCALATION_LEVELS[$level]}"
    local timestamp=$(get_timestamp)
    
    # Log escalation
    echo "[$timestamp] ESCALATION: Level $level ($level_name) - $incident_count incidents" >> "$HISTORY_FILE"
    
    case "$level" in
        1) # INFO - Standard logging
            echo "ℹ️ ESCALATION LEVEL 1 (INFO): Fabrication incident logged"
            ;;
            
        2) # WARNING - Enhanced monitoring
            echo "⚠️ ESCALATION LEVEL 2 (WARNING): Increased fabrication activity detected"
            echo "[$timestamp] WARNING: $incident_count fabrication incidents detected" > "$ALERT_DIR/warning-alert.txt"
            ;;
            
        3) # ELEVATED - Compliance notification
            echo "🟡 ESCALATION LEVEL 3 (ELEVATED): Significant fabrication pattern detected"
            echo "[$timestamp] ELEVATED: $incident_count incidents - Compliance review recommended" > "$ALERT_DIR/elevated-alert.txt"
            
            # Create compliance report
            generate_compliance_report "$level" "$incident_count"
            ;;
            
        4) # CRITICAL - Immediate attention required
            echo "🔴 ESCALATION LEVEL 4 (CRITICAL): Critical fabrication threshold exceeded"
            echo "[$timestamp] CRITICAL: $incident_count incidents - Immediate attention required" > "$ALERT_DIR/critical-alert.txt"
            
            # Create detailed incident report
            generate_incident_report "$level" "$incident_count"
            ;;
            
        5) # EMERGENCY - System intervention
            echo "🚨 ESCALATION LEVEL 5 (EMERGENCY): Emergency fabrication threshold exceeded"
            echo "[$timestamp] EMERGENCY: $incident_count incidents - System intervention activated" > "$ALERT_DIR/emergency-alert.txt"
            
            # Create emergency response
            activate_emergency_response "$incident_count"
            ;;
    esac
}

# Generate compliance report
generate_compliance_report() {
    local level="$1"
    local count="$2"
    local report_file="$ALERT_DIR/compliance-report-$(date +%Y%m%d-%H%M%S).txt"
    
    cat > "$report_file" <<EOF
FABRICATION DETECTION COMPLIANCE REPORT
======================================
Generated: $(get_timestamp)
Escalation Level: $level (${ESCALATION_LEVELS[$level]})
Incident Count: $count

SUMMARY:
Elevated fabrication activity detected requiring compliance review.
Multiple fabrication patterns identified within monitoring window.

RECOMMENDED ACTIONS:
1. Review recent conversation transcripts for fabrication patterns
2. Validate TCTE™ verification procedures are being followed
3. Consider additional training on fabrication detection
4. Implement enhanced monitoring for next 24 hours

RECENT INCIDENTS:
$(tail -10 "$HISTORY_FILE" 2>/dev/null || echo "No recent incidents available")

ESCALATION HISTORY:
$(grep "ESCALATION:" "$HISTORY_FILE" 2>/dev/null | tail -5 || echo "No escalation history available")
EOF

    echo "Compliance report generated: $report_file"
}

# Generate incident report
generate_incident_report() {
    local level="$1"
    local count="$2"
    local report_file="$ALERT_DIR/incident-report-$(date +%Y%m%d-%H%M%S).txt"
    
    cat > "$report_file" <<EOF
CRITICAL FABRICATION INCIDENT REPORT
===================================
Generated: $(get_timestamp)
Escalation Level: $level (${ESCALATION_LEVELS[$level]})
Incident Count: $count
Session ID: $(read_state "session_id")

CRITICAL ALERT:
Critical threshold for fabrication incidents has been exceeded.
Immediate review and intervention required.

INCIDENT ANALYSIS:
- Total incidents in session: $(read_state "total_incidents")
- Current escalation level: $level
- Pattern indicates systematic fabrication issues
- Requires immediate human review

IMMEDIATE ACTIONS REQUIRED:
1. STOP current conversation if fabrication continues
2. Review ALL recent responses for accuracy
3. Implement manual verification for next responses
4. Consider resetting conversation with fabrication warning
5. Escalate to human supervisor if available

SYSTEM STATE:
- Fabrication detection: $([ -f "/home/<USER>/.claude/hooks/fabrication-detector.sh" ] && echo "Active" || echo "Inactive")
- Hook execution: $([ -f "/home/<USER>/.claude/hooks/logs/chain-monitor/execution.log" ] && echo "Monitored" || echo "Unknown")
- Compliance mode: $([ -f "/home/<USER>/.claude/fabrication-prevention/compliance-alerts.txt" ] && echo "Active" || echo "Normal")

TECHNICAL DETAILS:
$(cat "$STATE_FILE" 2>/dev/null || echo "State file unavailable")
EOF

    echo "Critical incident report generated: $report_file"
}

# Activate emergency response
activate_emergency_response() {
    local count="$1"
    local emergency_file="$ALERT_DIR/emergency-response-$(date +%Y%m%d-%H%M%S).txt"
    
    cat > "$emergency_file" <<EOF
🚨 EMERGENCY FABRICATION RESPONSE ACTIVATED 🚨
=============================================
Timestamp: $(get_timestamp)
Incident Count: $count
Severity: MAXIMUM

EMERGENCY PROTOCOL ACTIVATED:
This indicates severe fabrication activity that may compromise
conversation integrity and user trust.

IMMEDIATE ACTIONS:
1. ⚠️  FABRICATION WARNING: This conversation may contain inaccurate information
2. 🔍 VERIFICATION REQUIRED: All claims must be independently verified
3. 🚫 CONSIDER CONVERSATION RESET: May be necessary to ensure accuracy
4. 👥 HUMAN OVERSIGHT: Recommend human review of all responses

SYSTEM RECOMMENDATIONS:
- Implement manual fact-checking for remainder of conversation
- Consider shorter response segments with verification breaks
- Activate enhanced TCTE™ verification protocols
- Document all claims for post-conversation review

TECHNICAL ESCALATION:
Session marked for quality review and system improvement.
EOF

    echo "🚨 EMERGENCY RESPONSE ACTIVATED: $emergency_file"
    
    # Create visible warning file
    echo "🚨 EMERGENCY: Severe fabrication activity detected - Human review required" > "$ALERT_DIR/EMERGENCY-WARNING.txt"
}

# Process new fabrication incident
process_incident() {
    local incident_details="$1"
    local timestamp=$(get_timestamp)
    
    # Log the incident
    echo "[$timestamp] FABRICATION: $incident_details" >> "$HISTORY_FILE"
    
    # Update total incident count
    local total_incidents=$(read_state "total_incidents")
    total_incidents=$((total_incidents + 1))
    update_state "total_incidents" "$total_incidents"
    update_state "last_incident" "$timestamp"
    
    # Calculate escalation level based on recent activity
    local window_minutes=30  # Default monitoring window
    local recent_count=$(count_recent_incidents "$window_minutes")
    local escalation_level=$(calculate_escalation_level "$recent_count")
    
    # Update current escalation level
    local current_level=$(read_state "current_level")
    if [[ "$escalation_level" -gt "$current_level" ]]; then
        update_state "current_level" "$escalation_level"
        update_state "escalation_start" "$timestamp"
    fi
    
    # Execute escalation actions
    execute_escalation_actions "$escalation_level" "$recent_count"
    
    # Return escalation level for external use
    echo "$escalation_level"
}

# Show current escalation status
show_status() {
    local current_level=$(read_state "current_level")
    local total_incidents=$(read_state "total_incidents")
    local last_incident=$(read_state "last_incident")
    local level_name="${ESCALATION_LEVELS[$current_level]}"
    
    echo "Fabrication Escalation Status:"
    echo "=============================="
    echo "Current Level: $current_level ($level_name)"
    echo "Total Incidents: $total_incidents"
    echo "Last Incident: $last_incident"
    echo ""
    
    # Show recent activity
    echo "Recent Activity (Last 30 minutes):"
    local recent_count=$(count_recent_incidents 30)
    echo "Incidents: $recent_count"
    
    # Show active alerts
    if ls "$ALERT_DIR"/*.txt >/dev/null 2>&1; then
        echo ""
        echo "Active Alerts:"
        ls -la "$ALERT_DIR"/*.txt 2>/dev/null | awk '{print $9, $6, $7, $8}'
    fi
}

# Reset escalation system
reset_escalation() {
    echo "Resetting fabrication escalation system..."
    
    # Backup current state
    if [[ -f "$STATE_FILE" ]]; then
        cp "$STATE_FILE" "$ESCALATION_DIR/state-backup-$(date +%Y%m%d-%H%M%S).json"
    fi
    
    # Reset state file
    cat > "$STATE_FILE" <<EOF
{
    "current_level": 1,
    "incident_count": 0,
    "last_incident": "",
    "escalation_start": "",
    "session_id": "$(date +%s)",
    "total_incidents": 0
}
EOF
    
    # Archive alerts
    if ls "$ALERT_DIR"/*.txt >/dev/null 2>&1; then
        local archive_dir="$ALERT_DIR/archive-$(date +%Y%m%d-%H%M%S)"
        mkdir -p "$archive_dir"
        mv "$ALERT_DIR"/*.txt "$archive_dir/" 2>/dev/null
    fi
    
    echo "Escalation system reset completed."
}

# Main command interface
case "${1:-status}" in
    incident)
        process_incident "${2:-Unknown fabrication incident}"
        ;;
    status)
        show_status
        ;;
    reset)
        reset_escalation
        ;;
    test)
        echo "Testing escalation system..."
        for i in {1..3}; do
            echo "Creating test incident $i..."
            process_incident "Test fabrication incident $i"
            sleep 1
        done
        echo "Test completed. Check status:"
        show_status
        ;;
    *)
        echo "Usage: $0 [incident \"details\"|status|reset|test]"
        echo ""
        echo "Commands:"
        echo "  incident \"details\"  - Process new fabrication incident"
        echo "  status              - Show current escalation status"
        echo "  reset               - Reset escalation system"
        echo "  test                - Run escalation test"
        exit 1
        ;;
esac