#!/bin/bash
# Fabrication Detection Dashboard Generator
# Purpose: Create HTML dashboard for fabrication incident monitoring
# Created: 2025-08-17

# Source utilities
source ~/.claude/hooks/lib/date-utils.sh 2>/dev/null || {
    get_timestamp() { date '+%Y-%m-%d %H:%M:%S'; }
}

# Configuration
DASHBOARD_DIR="$HOME/.claude/hooks/logs/dashboard"
DASHBOARD_FILE="$DASHBOARD_DIR/fabrication-dashboard.html"
LOG_FILE="$HOME/.claude/fabrication-prevention/logs/fabrication-incidents.log"
COMPLIANCE_ALERTS="$HOME/.claude/fabrication-prevention/compliance-alerts.txt"

# Ensure dashboard directory exists
mkdir -p "$DASHBOARD_DIR"

# Generate dashboard HTML
generate_dashboard() {
    local current_time=$(get_timestamp)
    local total_incidents=$(grep -c "PATTERN:" "$LOG_FILE" 2>/dev/null || echo "0")
    local high_risk=$(grep -c "HIGH.*PATTERN:" "$LOG_FILE" 2>/dev/null || echo "0")
    local critical_incidents=$(grep -c "CRITICAL.*PATTERN:" "$LOG_FILE" 2>/dev/null || echo "0")
    local compliance_incidents=$(grep -c "COMPLIANCE" "$LOG_FILE" 2>/dev/null || echo "0")
    local clean_prompts=$(grep -c "clean.*no_fabrication_detected" "$LOG_FILE" 2>/dev/null || echo "0")
    
    # Calculate detection rate
    local detection_rate=0
    local total_prompts=$((total_incidents + clean_prompts))
    if [ "$total_prompts" -gt 0 ]; then
        detection_rate=$((100 * total_incidents / total_prompts))
    fi
    
    # Get recent incidents
    local recent_incidents=$(tail -10 "$LOG_FILE" 2>/dev/null | grep "PATTERN:" | head -5)
    
    # Generate HTML
    cat > "$DASHBOARD_FILE" <<EOF
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fabrication Detection Dashboard</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            text-align: center;
        }
        .metric-value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .metric-label {
            color: #666;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .high-risk { color: #e74c3c; }
        .critical { color: #c0392b; }
        .compliance { color: #f39c12; }
        .clean { color: #27ae60; }
        .detection-rate { color: #3498db; }
        
        .incidents-section {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .incident-item {
            padding: 10px;
            border-left: 4px solid #3498db;
            margin: 10px 0;
            background: #f8f9fa;
            font-family: monospace;
            font-size: 0.9em;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
        }
        .status-active { background-color: #27ae60; }
        .status-warning { background-color: #f39c12; }
        .status-critical { background-color: #e74c3c; }
        
        .footer {
            text-align: center;
            color: #666;
            margin-top: 20px;
            font-size: 0.9em;
        }
        
        .refresh-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
        }
        .refresh-button:hover {
            background: #2980b9;
        }
    </style>
    <script>
        function refreshDashboard() {
            location.reload();
        }
        
        // Auto-refresh every 30 seconds
        setTimeout(function() {
            location.reload();
        }, 30000);
    </script>
</head>
<body>
    <div class="header">
        <h1>🔍 Fabrication Detection Dashboard</h1>
        <p>Real-time monitoring of TCTE™ fabrication detection system</p>
        <p>Last Updated: $current_time</p>
        <button class="refresh-button" onclick="refreshDashboard()">🔄 Refresh</button>
    </div>
    
    <div class="metrics-grid">
        <div class="metric-card">
            <div class="metric-value detection-rate">$detection_rate%</div>
            <div class="metric-label">Detection Rate</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">$total_incidents</div>
            <div class="metric-label">Total Incidents</div>
        </div>
        <div class="metric-card">
            <div class="metric-value high-risk">$high_risk</div>
            <div class="metric-label">High Risk</div>
        </div>
        <div class="metric-card">
            <div class="metric-value critical">$critical_incidents</div>
            <div class="metric-label">Critical</div>
        </div>
        <div class="metric-card">
            <div class="metric-value compliance">$compliance_incidents</div>
            <div class="metric-label">Compliance</div>
        </div>
        <div class="metric-card">
            <div class="metric-value clean">$clean_prompts</div>
            <div class="metric-label">Clean Prompts</div>
        </div>
    </div>
    
    <div class="incidents-section">
        <h2>🚨 Recent Fabrication Incidents</h2>
EOF

    # Add recent incidents
    if [[ -n "$recent_incidents" ]]; then
        echo "$recent_incidents" | while IFS= read -r line; do
            echo "        <div class=\"incident-item\">$line</div>" >> "$DASHBOARD_FILE"
        done
    else
        echo "        <div class=\"incident-item\">No recent incidents detected</div>" >> "$DASHBOARD_FILE"
    fi

    # Continue HTML generation
    cat >> "$DASHBOARD_FILE" <<EOF
    </div>
    
    <div class="incidents-section">
        <h2>📊 System Status</h2>
        <div style="margin: 20px 0;">
EOF

    # System status indicators
    if [[ -f "$LOG_FILE" ]]; then
        echo "            <span class=\"status-indicator status-active\"></span>Fabrication Detection: Active" >> "$DASHBOARD_FILE"
    else
        echo "            <span class=\"status-indicator status-critical\"></span>Fabrication Detection: Offline" >> "$DASHBOARD_FILE"
    fi
    
    if [[ -f "$COMPLIANCE_ALERTS" ]]; then
        echo "            <br><span class=\"status-indicator status-warning\"></span>Compliance Alerts: Active" >> "$DASHBOARD_FILE"
    else
        echo "            <br><span class=\"status-indicator status-active\"></span>Compliance: Normal" >> "$DASHBOARD_FILE"
    fi

    # Hook execution status
    local hook_status="Unknown"
    local last_execution=$(grep "fabrication-detector" /home/<USER>/.claude/hooks/logs/chain-monitor/execution.log 2>/dev/null | tail -1)
    if [[ -n "$last_execution" ]]; then
        hook_status="Active"
        echo "            <br><span class=\"status-indicator status-active\"></span>Hook Execution: $hook_status" >> "$DASHBOARD_FILE"
    else
        hook_status="Not Executing"
        echo "            <br><span class=\"status-indicator status-critical\"></span>Hook Execution: $hook_status" >> "$DASHBOARD_FILE"
    fi

    # Finish HTML
    cat >> "$DASHBOARD_FILE" <<EOF
        </div>
    </div>
    
    <div class="incidents-section">
        <h2>📈 Pattern Analysis</h2>
EOF

    # Pattern frequency analysis
    echo "        <div style=\"font-family: monospace; font-size: 0.9em;\">" >> "$DASHBOARD_FILE"
    
    # Count pattern frequencies
    local pattern_analysis=$(grep "PATTERN:" "$LOG_FILE" 2>/dev/null | sed 's/.*PATTERN:\([^[:space:]]*\).*/\1/' | sort | uniq -c | sort -nr | head -5)
    
    if [[ -n "$pattern_analysis" ]]; then
        echo "            <h4>Top Detected Patterns:</h4>" >> "$DASHBOARD_FILE"
        echo "$pattern_analysis" | while IFS= read -r line; do
            echo "            <div>$line</div>" >> "$DASHBOARD_FILE"
        done
    else
        echo "            <div>No pattern data available</div>" >> "$DASHBOARD_FILE"
    fi
    
    cat >> "$DASHBOARD_FILE" <<EOF
        </div>
    </div>
    
    <div class="footer">
        <p>🔒 TCTE™ Fabrication Detection System | Claude Code Hook Architecture</p>
        <p>Dashboard auto-refreshes every 30 seconds</p>
    </div>
</body>
</html>
EOF

    echo "Dashboard generated: $DASHBOARD_FILE"
}

# Generate analytics report
generate_analytics() {
    local analytics_file="$DASHBOARD_DIR/fabrication-analytics-$(date +%Y%m%d-%H%M%S).json"
    
    # Extract metrics
    local total_incidents=$(grep -c "PATTERN:" "$LOG_FILE" 2>/dev/null || echo "0")
    local high_risk=$(grep -c "HIGH.*PATTERN:" "$LOG_FILE" 2>/dev/null || echo "0")
    local critical_incidents=$(grep -c "CRITICAL.*PATTERN:" "$LOG_FILE" 2>/dev/null || echo "0")
    local compliance_incidents=$(grep -c "COMPLIANCE" "$LOG_FILE" 2>/dev/null || echo "0")
    local clean_prompts=$(grep -c "clean.*no_fabrication_detected" "$LOG_FILE" 2>/dev/null || echo "0")
    
    # Generate JSON analytics
    cat > "$analytics_file" <<EOF
{
    "timestamp": "$(get_timestamp)",
    "metrics": {
        "total_incidents": $total_incidents,
        "high_risk_incidents": $high_risk,
        "critical_incidents": $critical_incidents,
        "compliance_incidents": $compliance_incidents,
        "clean_prompts": $clean_prompts,
        "detection_rate": $((100 * total_incidents / (total_incidents + clean_prompts + 1)))
    },
    "system_status": {
        "log_file_exists": $([ -f "$LOG_FILE" ] && echo "true" || echo "false"),
        "compliance_alerts_active": $([ -f "$COMPLIANCE_ALERTS" ] && echo "true" || echo "false"),
        "dashboard_functional": true
    },
    "top_patterns": [
EOF

    # Add top patterns to JSON
    local patterns=$(grep "PATTERN:" "$LOG_FILE" 2>/dev/null | sed 's/.*PATTERN:\([^[:space:]]*\).*/\1/' | sort | uniq -c | sort -nr | head -3)
    local pattern_count=0
    
    echo "$patterns" | while IFS= read -r line; do
        if [[ -n "$line" ]]; then
            local count=$(echo "$line" | awk '{print $1}')
            local pattern=$(echo "$line" | awk '{$1=""; print $0}' | sed 's/^ *//')
            
            if [ $pattern_count -gt 0 ]; then
                echo "        ," >> "$analytics_file"
            fi
            echo "        {\"pattern\": \"$pattern\", \"count\": $count}" >> "$analytics_file"
            ((pattern_count++))
        fi
    done
    
    echo "    ]" >> "$analytics_file"
    echo "}" >> "$analytics_file"
    
    echo "Analytics generated: $analytics_file"
}

# Main execution
case "${1:-dashboard}" in
    dashboard)
        generate_dashboard
        ;;
    analytics)
        generate_analytics
        ;;
    both)
        generate_dashboard
        generate_analytics
        ;;
    *)
        echo "Usage: $0 [dashboard|analytics|both]"
        exit 1
        ;;
esac