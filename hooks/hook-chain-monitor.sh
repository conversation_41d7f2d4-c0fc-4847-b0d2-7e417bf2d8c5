#!/bin/bash
# Hook Chain Execution Monitor
# Purpose: Track hook chain execution health and detect termination events
# Created: 2025-08-17

# Source utilities
source ~/.claude/hooks/lib/date-utils.sh 2>/dev/null || {
    get_timestamp() { date '+%Y-%m-%d %H:%M:%S'; }
}

# Configuration
LOG_DIR="$HOME/.claude/hooks/logs/chain-monitor"
EXECUTION_LOG="$LOG_DIR/execution.log"
TERMINATION_LOG="$LOG_DIR/terminations.log"
HEALTH_LOG="$LOG_DIR/health.log"

# Ensure log directory exists
mkdir -p "$LOG_DIR"
touch "$EXECUTION_LOG" "$TERMINATION_LOG" "$HEALTH_LOG"

# Get hook name from command line or process info
HOOK_NAME="${1:-unknown}"
if [[ -z "$1" ]]; then
    # Try to extract hook name from calling script
    HOOK_NAME=$(basename "${BASH_SOURCE[1]}" .sh 2>/dev/null || echo "unknown")
fi

# Get execution status (passed as second argument or from previous command)
HOOK_STATUS="${2:-$?}"

# Log hook execution attempt
log_hook_execution() {
    local hook_name="$1"
    local status="$2"
    local timestamp=$(get_timestamp)
    
    # Log to execution log
    echo "[$timestamp] Hook: $hook_name Status: $status" >> "$EXECUTION_LOG"
    
    # Log termination events
    if [ "$status" -ne 0 ]; then
        echo "[$timestamp] CHAIN TERMINATED at $hook_name (exit code: $status)" >> "$TERMINATION_LOG"
        
        # Create alert for immediate termination
        echo "[$timestamp] ALERT: Hook chain terminated at $hook_name" > "$LOG_DIR/latest-termination.alert"
    fi
}

# Health check function
check_hook_health() {
    local hook_count=$(grep -c "Hook:" "$EXECUTION_LOG" 2>/dev/null || echo "0")
    local termination_count=$(grep -c "TERMINATED" "$TERMINATION_LOG" 2>/dev/null || echo "0")
    local success_rate=0
    
    if [ "$hook_count" -gt 0 ]; then
        success_rate=$((100 * (hook_count - termination_count) / hook_count))
    fi
    
    echo "[$(get_timestamp)] Total: $hook_count Terminations: $termination_count Success Rate: $success_rate%" >> "$HEALTH_LOG"
}

# Performance tracking
track_performance() {
    local hook_name="$1"
    local start_time="$2"
    local end_time="$3"
    
    if [[ -n "$start_time" && -n "$end_time" ]]; then
        local duration=$((end_time - start_time))
        echo "[$(get_timestamp)] Performance: $hook_name executed in ${duration}ms" >> "$LOG_DIR/performance.log"
    fi
}

# Chain position tracking
track_chain_position() {
    local hook_name="$1"
    local position="$2"
    
    echo "[$(get_timestamp)] Position: $position Hook: $hook_name" >> "$LOG_DIR/chain-order.log"
}

# Main monitoring function
main() {
    local hook_name="$1"
    local status="$2"
    
    # Log the execution
    log_hook_execution "$hook_name" "$status"
    
    # Update health metrics
    check_hook_health
    
    # Create summary for dashboard
    if [[ "$hook_name" == "fabrication-detector" ]]; then
        echo "[$(get_timestamp)] FABRICATION DETECTOR: Status $status" >> "$LOG_DIR/fabrication-tracking.log"
    fi
    
    # Real-time alerting for critical hooks
    if [[ "$status" -ne 0 && "$hook_name" =~ (fabrication|security|compliance) ]]; then
        echo "[$(get_timestamp)] CRITICAL: $hook_name failed with status $status" > "$LOG_DIR/critical-failure.alert"
    fi
}

# Usage information
show_usage() {
    echo "Usage: $0 [hook_name] [exit_status]"
    echo "       $0 --health     # Show health summary"
    echo "       $0 --reset      # Reset monitoring logs"
    echo "       $0 --report     # Generate monitoring report"
}

# Health summary
show_health() {
    echo "Hook Chain Health Summary:"
    echo "=========================="
    if [[ -f "$HEALTH_LOG" ]]; then
        tail -5 "$HEALTH_LOG"
    else
        echo "No health data available"
    fi
    
    echo ""
    echo "Recent Terminations:"
    echo "==================="
    if [[ -f "$TERMINATION_LOG" ]]; then
        tail -3 "$TERMINATION_LOG"
    else
        echo "No terminations recorded"
    fi
}

# Reset monitoring logs
reset_logs() {
    echo "Resetting hook chain monitoring logs..."
    > "$EXECUTION_LOG"
    > "$TERMINATION_LOG"
    > "$HEALTH_LOG"
    rm -f "$LOG_DIR"/*.alert
    echo "Logs reset at $(get_timestamp)"
}

# Generate monitoring report
generate_report() {
    local report_file="$LOG_DIR/monitor-report-$(date +%Y%m%d-%H%M%S).txt"
    
    {
        echo "Hook Chain Monitoring Report"
        echo "Generated: $(get_timestamp)"
        echo "=========================="
        echo ""
        
        echo "Execution Summary:"
        if [[ -f "$EXECUTION_LOG" ]]; then
            echo "Total executions: $(wc -l < "$EXECUTION_LOG")"
            echo "Recent executions:"
            tail -10 "$EXECUTION_LOG"
        fi
        
        echo ""
        echo "Termination Summary:"
        if [[ -f "$TERMINATION_LOG" ]]; then
            echo "Total terminations: $(wc -l < "$TERMINATION_LOG")"
            echo "Recent terminations:"
            tail -5 "$TERMINATION_LOG"
        fi
        
        echo ""
        echo "Health Metrics:"
        if [[ -f "$HEALTH_LOG" ]]; then
            tail -5 "$HEALTH_LOG"
        fi
        
    } > "$report_file"
    
    echo "Report generated: $report_file"
}

# Handle command line arguments
case "${1:-}" in
    --health)
        show_health
        ;;
    --reset)
        reset_logs
        ;;
    --report)
        generate_report
        ;;
    --help|-h)
        show_usage
        ;;
    *)
        # Normal monitoring mode
        main "$@"
        ;;
esac