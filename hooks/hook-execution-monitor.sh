#!/bin/bash
# Hook Execution Monitor for Diagnostics
# This script logs whenever it's executed to verify hook system functionality

MONITOR_LOG="/home/<USER>/.claude/monitoring/logs/hook-execution-monitor.log"
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')

echo "[$TIMESTAMP] HOOK_EXECUTION_MONITOR: UserPromptSubmit hook executed with args: $*" >> "$MONITOR_LOG"
echo "[$TIMESTAMP] HOOK_EXECUTION_MONITOR: STDIN available: $(test -p /dev/stdin && echo 'YES' || echo 'NO')" >> "$MONITOR_LOG"
echo "[$TIMESTAMP] HOOK_EXECUTION_MONITOR: Environment: PWD=$PWD, USER=$USER" >> "$MONITOR_LOG"

# Try to read stdin if available
if [[ -p /dev/stdin ]]; then
    local stdin_content=$(timeout 1 cat /dev/stdin 2>/dev/null || echo "TIMEOUT")
    echo "[$TIMESTAMP] HOOK_EXECUTION_MONITOR: STDIN content: $stdin_content" >> "$MONITOR_LOG"
fi

# Always return success to not block Claude
exit 0
