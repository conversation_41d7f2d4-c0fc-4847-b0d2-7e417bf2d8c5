#!/bin/bash
# UserPromptSubmit Test Logger - Definitive Hook Execution Test
# Purpose: Log every UserPromptSubmit hook execution to prove hooks are working

LOG_FILE="/tmp/userpromptsubmit-test.log"

# Log the execution with timestamp
echo "[$(date '+%Y-%m-%d %H:%M:%S')] UserPromptSubmit hook EXECUTED - PID: $$ - Session: $(ps -p $PPID -o comm= 2>/dev/null || echo 'unknown')" >> "$LOG_FILE"

# Try to read JSON from stdin if available
if [ -p /dev/stdin ]; then
    JSON_INPUT=$(timeout 1 cat 2>/dev/null || echo "")
    if [ -n "$JSON_INPUT" ]; then
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] JSON INPUT RECEIVED: $JSON_INPUT" >> "$LOG_FILE"
        # Extract prompt if JSON contains it
        if command -v jq >/dev/null 2>&1; then
            PROMPT=$(echo "$JSON_INPUT" | jq -r '.prompt // "no-prompt"' 2>/dev/null)
            echo "[$(date '+%Y-%m-%d %H:%M:%S')] EXTRACTED PROMPT: $PROMPT" >> "$LOG_FILE"
        fi
    else
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] NO JSON INPUT - stdin empty or not available" >> "$LOG_FILE"
    fi
else
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] NO STDIN PIPE DETECTED" >> "$LOG_FILE"
fi

# Check for command line arguments
if [ $# -gt 0 ]; then
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] COMMAND LINE ARGS: $*" >> "$LOG_FILE"
else
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] NO COMMAND LINE ARGS" >> "$LOG_FILE"
fi

echo "[$(date '+%Y-%m-%d %H:%M:%S')] --- END EXECUTION ---" >> "$LOG_FILE"

# Always return success to not block conversation
exit 0