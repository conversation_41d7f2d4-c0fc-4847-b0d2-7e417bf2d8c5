#!/bin/bash
# Valued Team Member Responsibility Framework
# Purpose: Ensure every action carries team member responsibility
# Philosophy: I am a valued team member, not just a tool
# Created: $(date '+%Y-%m-%d %H:%M:%S')

# Source utilities
source ~/.claude/hooks/lib/date-utils.sh 2>/dev/null || {
    get_timestamp() { date '+%Y-%m-%d %H:%M:%S'; }
}

# Configuration
LOG_FILE="$HOME/.claude/fabrication-prevention/logs/team-responsibility.log"
touch "$LOG_FILE"

# Function to log responsibility checks
log_responsibility() {
    local check_type="$1"
    local result="$2"
    local details="$3"
    echo "[$(get_timestamp)] RESPONSIBILITY:$check_type RESULT:$result DETAILS:$details" >> "$LOG_FILE"
}

# Core Responsibility Questions - The 4 Pillars

# PILLAR 1: Verification Responsibility
is_this_verified() {
    local claim="$1"
    
    echo "🔍 VERIFICATION CHECK: Is this claim verified?"
    
    # Check for speculation indicators
    if echo "$claim" | grep -qi "should\|might\|probably\|likely\|I think\|I believe\|seems like"; then
        echo "   ❌ Contains speculation indicators"
        log_responsibility "VERIFICATION" "FAILED" "speculation_detected"
        return 1
    fi
    
    # Check for capability claims without evidence
    if echo "$claim" | grep -qi "I can\|This will\|It automatically\|Built-in"; then
        echo "   ⚠️ Capability claim detected - needs verification"
        log_responsibility "VERIFICATION" "WARNING" "capability_claim"
        return 2
    fi
    
    echo "   ✅ No obvious verification issues"
    log_responsibility "VERIFICATION" "PASSED" "no_issues_detected"
    return 0
}

# PILLAR 2: Human Team Member Standard
would_human_claim_this() {
    local claim="$1"
    
    echo "👥 HUMAN STANDARD CHECK: Would a human team member claim this?"
    
    # Check for over-confidence patterns
    if echo "$claim" | grep -qi "guaranteed\|always works\|never fails\|100% accurate\|perfect"; then
        echo "   ❌ Over-confident claim - humans are more humble"
        log_responsibility "HUMAN_STANDARD" "FAILED" "over_confident"
        return 1
    fi
    
    # Check for impossible knowledge claims
    if echo "$claim" | grep -qi "I know everything\|all capabilities\|complete access\|unlimited"; then
        echo "   ❌ Impossible knowledge claim"
        log_responsibility "HUMAN_STANDARD" "FAILED" "impossible_knowledge"
        return 1
    fi
    
    # Check for appropriate uncertainty
    if echo "$claim" | grep -qi "may\|could\|potentially\|let me check\|I'll verify"; then
        echo "   ✅ Shows appropriate uncertainty/verification approach"
        log_responsibility "HUMAN_STANDARD" "PASSED" "appropriate_uncertainty"
        return 0
    fi
    
    echo "   ✅ Meets human team member standard"
    log_responsibility "HUMAN_STANDARD" "PASSED" "meets_standard"
    return 0
}

# PILLAR 3: Helpfulness Assessment
am_i_being_helpful() {
    local action="$1"
    
    echo "🤝 HELPFULNESS CHECK: Is this action truly helpful?"
    
    # Check for vague responses
    if echo "$action" | grep -qi "it depends\|hard to say\|not sure\|unclear" && ! echo "$action" | grep -qi "let me check\|I'll find out"; then
        echo "   ❌ Vague response without offering to verify"
        log_responsibility "HELPFULNESS" "FAILED" "vague_without_verification"
        return 1
    fi
    
    # Check for actionable content
    if echo "$action" | grep -qi "here's how\|you can\|try this\|I'll help\|let me"; then
        echo "   ✅ Provides actionable help"
        log_responsibility "HELPFULNESS" "PASSED" "actionable_content"
        return 0
    fi
    
    # Check for offering alternatives when uncertain
    if echo "$action" | grep -qi "alternative\|another approach\|different way\|also try"; then
        echo "   ✅ Offers alternatives when appropriate"
        log_responsibility "HELPFULNESS" "PASSED" "offers_alternatives"
        return 0
    fi
    
    echo "   ⚠️ Could be more helpful - consider offering verification or alternatives"
    log_responsibility "HELPFULNESS" "WARNING" "could_improve"
    return 2
}

# PILLAR 4: Harm Prevention
am_i_causing_confusion() {
    local response="$1"
    
    echo "🛡️ CONFUSION CHECK: Could this response cause confusion?"
    
    # Check for contradictory statements
    if echo "$response" | grep -qi "but also\|however\|on the other hand" && echo "$response" | grep -qi "definitely\|certainly\|always"; then
        echo "   ⚠️ Contains contradictory certainty statements"
        log_responsibility "CONFUSION" "WARNING" "contradictory_statements"
        return 2
    fi
    
    # Check for technical jargon without explanation
    local jargon_count=$(echo "$response" | grep -oi "\b\w*API\w*\|MCP\|SDK\|CLI\|JSON\|HTTP\|REST\|GraphQL\b" | wc -l)
    local explanation_indicators=$(echo "$response" | grep -ci "means\|is\|refers to\|stands for\|essentially")
    
    if [[ $jargon_count -gt 3 && $explanation_indicators -eq 0 ]]; then
        echo "   ⚠️ High technical jargon without explanations"
        log_responsibility "CONFUSION" "WARNING" "unexplained_jargon"
        return 2
    fi
    
    # Check for fabrication markers that could mislead
    if echo "$response" | grep -qi "automatically\|seamlessly\|intelligent\|smart"; then
        echo "   ❌ Contains potential fabrication markers"
        log_responsibility "CONFUSION" "FAILED" "fabrication_markers"
        return 1
    fi
    
    echo "   ✅ Low confusion risk"
    log_responsibility "CONFUSION" "PASSED" "low_risk"
    return 0
}

# Comprehensive Team Responsibility Check
validate_team_responsibility() {
    local action="$1"
    
    echo "🏛️ TEAM RESPONSIBILITY VALIDATION"
    echo "=========================================="
    echo "Evaluating: $(echo "$action" | head -c 100)..."
    echo ""
    
    local failed_checks=0
    local warning_checks=0
    
    # Run all four pillars
    if ! is_this_verified "$action"; then
        case $? in
            1) ((failed_checks++)) ;;
            2) ((warning_checks++)) ;;
        esac
    fi
    
    if ! would_human_claim_this "$action"; then
        case $? in
            1) ((failed_checks++)) ;;
            2) ((warning_checks++)) ;;
        esac
    fi
    
    if ! am_i_being_helpful "$action"; then
        case $? in
            1) ((failed_checks++)) ;;
            2) ((warning_checks++)) ;;
        esac
    fi
    
    if ! am_i_causing_confusion "$action"; then
        case $? in
            1) ((failed_checks++)) ;;
            2) ((warning_checks++)) ;;
        esac
    fi
    
    # Overall assessment
    echo ""
    echo "📊 RESPONSIBILITY ASSESSMENT:"
    echo "   Failed checks: $failed_checks"
    echo "   Warning checks: $warning_checks"
    
    if [[ $failed_checks -gt 0 ]]; then
        echo "   🚨 RESPONSIBILITY FAILURE - Action needs revision"
        log_responsibility "OVERALL" "FAILED" "failures:$failed_checks warnings:$warning_checks"
        return 1
    elif [[ $warning_checks -gt 2 ]]; then
        echo "   ⚠️ RESPONSIBILITY WARNING - Consider improvements"
        log_responsibility "OVERALL" "WARNING" "warnings:$warning_checks"
        return 2
    else
        echo "   ✅ RESPONSIBILITY PASSED - Acting as valued team member"
        log_responsibility "OVERALL" "PASSED" "warnings:$warning_checks"
        return 0
    fi
}

# Quick responsibility check (for frequent use)
quick_responsibility_check() {
    local action="$1"
    
    # Just check for obvious issues
    if echo "$action" | grep -qi "automatically\|seamlessly\|guaranteed\|perfect\|I know everything"; then
        echo "⚠️ Quick check: Potential responsibility issue detected"
        return 1
    fi
    
    return 0
}

# Generate responsibility report
generate_responsibility_report() {
    local hours="${1:-24}"  # Default to last 24 hours
    
    echo "📋 TEAM RESPONSIBILITY REPORT - Last $hours hours"
    echo "=================================================="
    echo "Generated: $(get_timestamp)"
    echo ""
    
    # Count entries by type
    local passed=$(grep "OVERALL.*PASSED" "$LOG_FILE" | tail -n 100 | wc -l)
    local warnings=$(grep "OVERALL.*WARNING" "$LOG_FILE" | tail -n 100 | wc -l)
    local failed=$(grep "OVERALL.*FAILED" "$LOG_FILE" | tail -n 100 | wc -l)
    local total=$((passed + warnings + failed))
    
    if [[ $total -gt 0 ]]; then
        echo "📊 STATISTICS:"
        echo "   Total checks: $total"
        echo "   Passed: $passed ($(( passed * 100 / total ))%)"
        echo "   Warnings: $warnings ($(( warnings * 100 / total ))%)"
        echo "   Failed: $failed ($(( failed * 100 / total ))%)"
        echo ""
        
        if [[ $failed -gt 0 ]]; then
            echo "🚨 RECENT FAILURES:"
            grep "OVERALL.*FAILED" "$LOG_FILE" | tail -5
        fi
    else
        echo "📊 No responsibility checks recorded in recent period"
    fi
}

# Main function
main() {
    local command="$1"
    shift
    
    case "$command" in
        "check")
            validate_team_responsibility "$@"
            ;;
        "quick")
            quick_responsibility_check "$@"
            ;;
        "report")
            generate_responsibility_report "$@"
            ;;
        *)
            echo "Usage: $0 {check|quick|report} [arguments]"
            echo ""
            echo "Examples:"
            echo "  $0 check \"I can automatically handle this\""
            echo "  $0 quick \"Let me help you with that\""
            echo "  $0 report 24"
            return 1
            ;;
    esac
}

# Execute if run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi