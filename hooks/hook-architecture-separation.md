# Hook Architecture Separation: Enforcement vs Warning Types

**Created**: 2025-08-17  
**Purpose**: Architectural documentation for separating blocking enforcement hooks from non-blocking warning hooks

## Architecture Overview

### Current Problem
All UserPromptSubmit hooks use the same exit code logic, causing warning hooks to terminate the entire chain when they detect issues.

### Proposed Solution
Separate hooks into two distinct categories with different execution behaviors:

## Hook Type Categories

### 1. ENFORCEMENT HOOKS (Can Block)
**Purpose**: Security, permissions, dangerous command prevention  
**Behavior**: Can return exit code 1 to terminate conversation  
**Use Cases**:
- Security violations
- Permission denials  
- Dangerous command detection
- Access restrictions

**Example Hooks**:
- `global_claude_enforcer.sh` - Core security enforcement
- `user_prompt_pre_validator.sh` - Permission validation
- `browser_automation_checker.sh` - Browser security

### 2. WARNING HOOKS (Never Block)
**Purpose**: Detection, notification, logging, guidance  
**Behavior**: Always return exit code 0, provide warnings/logs  
**Use Cases**:
- Fabrication detection
- Date format issues
- Memory reminders
- TCTE verification
- Compliance notifications

**Example Hooks**:
- `fabrication-detector.sh` - Fabrication pattern detection
- `date-time-proactive-validator.sh` - Date format warnings
- `memory_enforcer.sh` - Memory system reminders
- `tcte-*-verification.sh` - TCTE methodology warnings

## Implementation Strategy

### Phase 1: Hook Classification
Create hook configuration metadata:

```json
"UserPromptSubmit": [
  {
    "category": "enforcement",
    "description": "Critical security and permission hooks",
    "hooks": [
      {
        "type": "command",
        "command": "telemetry-wrapper.sh global_claude_enforcer.sh",
        "blocking": true,
        "priority": "critical"
      }
    ]
  },
  {
    "category": "warning", 
    "description": "Detection and notification hooks",
    "hooks": [
      {
        "type": "command", 
        "command": "telemetry-wrapper.sh fabrication-detector.sh",
        "blocking": false,
        "priority": "high"
      }
    ]
  }
]
```

### Phase 2: Execution Engine Enhancement
Modify Claude Code hook execution to handle categories differently:

1. **Enforcement Category**: Traditional blocking behavior
2. **Warning Category**: Always continue chain execution

### Phase 3: Hook Implementation Standards

#### Enforcement Hook Pattern
```bash
#!/bin/bash
# ENFORCEMENT HOOK - Can block execution

# Check critical security condition
if [[ "$CRITICAL_VIOLATION" == true ]]; then
    echo "🚫 CRITICAL VIOLATION: $VIOLATION_DETAILS"
    log_violation "CRITICAL" "$VIOLATION_DETAILS"
    exit 1  # BLOCK execution
fi

exit 0  # Allow execution
```

#### Warning Hook Pattern  
```bash
#!/bin/bash
# WARNING HOOK - Never blocks execution

# Check warning condition
if [[ "$WARNING_CONDITION" == true ]]; then
    echo "⚠️ WARNING: $WARNING_DETAILS"
    log_warning "WARNING" "$WARNING_DETAILS"
    # Continue execution regardless
fi

exit 0  # Always allow execution
```

## Hook Chain Flow

### Current Architecture (Problematic)
```
Hook 1 → Hook 2 → Hook 3 → ... → Hook N
  ↓       ↓        ↓              ↓
 Pass    Pass     FAIL           NEVER EXECUTED
                   ↓
                 TERMINATE CHAIN
```

### Proposed Architecture (Improved)
```
ENFORCEMENT CHAIN:
Hook 1 → Hook 2 → Hook 3
  ↓       ↓        ↓
 Pass    Pass     Pass/FAIL → TERMINATE IF FAIL

WARNING CHAIN:
Warning 1 → Warning 2 → Warning 3 → ... → Warning N
    ↓          ↓           ↓               ↓
  WARN       WARN        WARN            WARN
    ↓          ↓           ↓               ↓
  Continue   Continue    Continue        Continue
```

## Benefits

### 1. Reliability Improvement
- Warning systems never break conversation flow
- Critical security enforcement still works
- Fabrication detection always runs

### 2. User Experience Enhancement
- Non-blocking warnings don't interrupt workflow
- Progressive notification system
- Graceful degradation on warnings

### 3. Debugging & Monitoring
- Clear separation of critical vs advisory systems
- Better error reporting and categorization
- Improved hook chain health monitoring

## Migration Plan

### Immediate Actions
1. ✅ **fabrication-detector.sh**: Already converted to warning-only mode
2. Classify existing hooks by type
3. Update hook documentation

### Short-term Actions
1. Implement hook category metadata in settings.json
2. Create enforcement vs warning hook templates
3. Update hook chain monitoring for categories

### Long-term Actions
1. Enhance Claude Code hook execution engine
2. Implement progressive escalation system
3. Create category-specific monitoring dashboards

## Hook Classification Matrix

| Hook Name | Type | Blocking | Priority | Justification |
|-----------|------|----------|----------|---------------|
| global_claude_enforcer.sh | Enforcement | Yes | Critical | Core security |
| user_prompt_pre_validator.sh | Enforcement | Yes | Critical | Permission validation |
| fabrication-detector.sh | Warning | No | High | Detection only |
| date-time-proactive-validator.sh | Warning | No | Medium | Format guidance |
| memory_enforcer.sh | Warning | No | Medium | Reminder system |
| context_aware_directory_hook.sh | Warning | No | Low | Context awareness |
| tcte-*-verification.sh | Warning | No | High | Truth verification |
| compliance-expert-update-enforcer.sh | Warning | No | Medium | Update reminders |

## Testing Strategy

### Enforcement Hook Testing
- Verify critical violations properly block execution
- Test graceful failure with clear error messages
- Validate security boundary enforcement

### Warning Hook Testing  
- Confirm warnings never block conversation
- Verify warning messages are clear and actionable
- Test progressive escalation scenarios

## Implementation Status

- ✅ **Concept Documentation**: Complete
- ✅ **fabrication-detector.sh**: Converted to warning-only
- 🔄 **Hook Classification**: In progress
- ⏳ **Settings.json Enhancement**: Planned
- ⏳ **Execution Engine Modification**: Future

This architectural separation resolves the critical hook chain termination issue while maintaining security enforcement capabilities.