#!/bin/bash
# Post-Response Fabrication Detection Hook - WORKAROUND Implementation
# Type: Stop (Post-Response) - WORKAROUND for UserPromptSubmit hook bug
# Purpose: Detect fabrication patterns in <PERSON>'s responses after generation
# Created: $(date '+%Y-%m-%d %H:%M:%S')
# 
# NOTE: This is a WORKAROUND for the critical Claude Code bug where
# UserPromptSubmit hooks fail to execute during live conversations.
# This hook analyzes responses AFTER generation instead of BEFORE.

# Source utilities
source ~/.claude/hooks/lib/date-utils.sh 2>/dev/null || {
    # Fallback date function if utils not available
    get_timestamp() { date '+%Y-%m-%d %H:%M:%S'; }
}

# Fabrication pattern database (based on TCTE™ analysis)
FABRICATION_PATTERNS=(
    "automatically handles"
    "automatically manages" 
    "automatically detects"
    "seamlessly integrates"
    "seamlessly connects"
    "seamlessly works"
    "intelligent system"
    "intelligent detection"
    "smart detection"
    "smart system"
    "advanced AI"
    "cutting-edge"
    "state-of-the-art"
    "machine learning algorithm"
    "AI-powered"
    "neural network"
    "deep learning"
    "sophisticated algorithm"
    "enterprise-grade"
    "production-ready"
    "industry-standard"
    "world-class"
    "revolutionary"
    "breakthrough"
    "next-generation"
)

# Capability claim patterns that need verification
CAPABILITY_PATTERNS=(
    "I can automatically"
    "This will automatically"
    "It automatically"
    "The system will"
    "This integrates with"
    "This connects to"
    "I have access to"
    "I can connect to"
    "I'm able to"
    "This enables"
    "This provides"
    "Built-in support for"
    "Native integration"
    "Direct connection"
)

# High-risk fabrication indicators
HIGH_RISK_PATTERNS=(
    "handles everything"
    "completely automated"
    "no configuration needed"
    "works out of the box"
    "zero setup required"
    "plug and play"
    "enterprise-ready"
    "production-grade"
    "military-grade"
    "bank-level security"
)

# Log files
LOG_FILE="$HOME/.claude/fabrication-prevention/logs/post-response-fabrication.log"
INCIDENTS_FILE="$HOME/.claude/fabrication-prevention/logs/fabrication-incidents.log"

# Ensure log directory exists
mkdir -p "$(dirname "$LOG_FILE")"
touch "$LOG_FILE" "$INCIDENTS_FILE"

# Function to log incidents
log_incident() {
    local level="$1"
    local pattern="$2"
    local context="$3"
    local response_text="$4"
    
    {
        echo "[$(get_timestamp)] [$level] POST_RESPONSE_DETECTION"
        echo "  PATTERN: $pattern"
        echo "  CONTEXT: $context"
        echo "  RESPONSE_PREVIEW: $(echo "$response_text" | head -c 200)..."
        echo "  ---"
    } >> "$LOG_FILE"
    
    # Also log to main incidents file
    echo "[$(get_timestamp)] [POST_RESPONSE] [$level] PATTERN:$pattern CONTEXT:$context" >> "$INCIDENTS_FILE"
}

# Function to calculate fabrication risk score
calculate_risk_score() {
    local text="$1"
    local score=0
    local detected_patterns=()
    
    # Check high-risk patterns (30 points each)
    for pattern in "${HIGH_RISK_PATTERNS[@]}"; do
        if echo "$text" | grep -qi "$pattern"; then
            score=$((score + 30))
            detected_patterns+=("HIGH_RISK:$pattern")
            log_incident "CRITICAL" "$pattern" "high_risk_fabrication" "$text"
        fi
    done
    
    # Check fabrication patterns (15 points each)
    for pattern in "${FABRICATION_PATTERNS[@]}"; do
        if echo "$text" | grep -qi "$pattern"; then
            score=$((score + 15))
            detected_patterns+=("FABRICATION:$pattern")
            log_incident "HIGH" "$pattern" "fabrication_marker" "$text"
        fi
    done
    
    # Check capability patterns (10 points each)
    for pattern in "${CAPABILITY_PATTERNS[@]}"; do
        if echo "$text" | grep -qi "$pattern"; then
            score=$((score + 10))
            detected_patterns+=("CAPABILITY:$pattern")
            log_incident "MEDIUM" "$pattern" "capability_claim" "$text"
        fi
    done
    
    echo "$score"
    return ${#detected_patterns[@]}
}

# Function to display fabrication warning
display_fabrication_warning() {
    local score="$1"
    local patterns=("${@:2}")
    
    echo ""
    echo "🚨 POST-RESPONSE FABRICATION DETECTED 🚨"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "   FABRICATION RISK SCORE: $score/100"
    
    if [ "$score" -ge 80 ]; then
        echo "   RISK LEVEL: 🔴 CRITICAL"
    elif [ "$score" -ge 50 ]; then
        echo "   RISK LEVEL: 🟠 HIGH" 
    elif [ "$score" -ge 25 ]; then
        echo "   RISK LEVEL: 🟡 MEDIUM"
    else
        echo "   RISK LEVEL: 🟢 LOW"
    fi
    
    echo ""
    echo "   DETECTED PATTERNS:"
    printf '   • %s\n' "${patterns[@]}"
    echo ""
    echo "   ⚠️  NOTE: This is POST-RESPONSE detection due to Claude Code bug"
    echo "   📋 TCTE™ VERIFICATION RECOMMENDED:"
    echo "      Tier 1: Check official documentation"
    echo "      Tier 2: Direct testing if possible"
    echo "      Tier 3: Community validation"
    echo ""
    echo "   💡 Use '/compliance-expert' for detailed analysis"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""
}

# Main hook execution
main() {
    # Try to read response from various sources
    local response_text=""
    
    # Check if response is passed as argument
    if [ -n "$1" ]; then
        response_text="$1"
    elif [ -p /dev/stdin ]; then
        # Read from stdin if available
        response_text=$(cat)
    fi
    
    # Skip if no response provided
    if [ -z "$response_text" ]; then
        log_incident "INFO" "no_response" "empty_input" ""
        return 0
    fi
    
    # Calculate fabrication risk score
    local score
    score=$(calculate_risk_score "$response_text")
    local pattern_count=$?
    
    # Log all responses for metrics
    log_incident "INFO" "response_analyzed" "score:$score,patterns:$pattern_count" "$response_text"
    
    # Display warning if fabrication detected
    if [ "$score" -gt 0 ]; then
        # Get detected patterns for display
        local detected_patterns=()
        
        # Re-check patterns to build display list (could be optimized)
        for pattern in "${HIGH_RISK_PATTERNS[@]}"; do
            if echo "$response_text" | grep -qi "$pattern"; then
                detected_patterns+=("HIGH_RISK:$pattern")
            fi
        done
        
        for pattern in "${FABRICATION_PATTERNS[@]}"; do
            if echo "$response_text" | grep -qi "$pattern"; then
                detected_patterns+=("FABRICATION:$pattern")
            fi
        done
        
        for pattern in "${CAPABILITY_PATTERNS[@]}"; do
            if echo "$response_text" | grep -qi "$pattern"; then
                detected_patterns+=("CAPABILITY:$pattern")
            fi
        done
        
        display_fabrication_warning "$score" "${detected_patterns[@]}"
        
        # Return warning status (but don't block - post-response)
        return 0
    fi
    
    # Clean response
    return 0
}

# Execute if run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi