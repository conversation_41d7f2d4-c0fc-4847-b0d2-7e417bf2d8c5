<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fabrication Detection Dashboard</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            text-align: center;
        }
        .metric-value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .metric-label {
            color: #666;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .high-risk { color: #e74c3c; }
        .critical { color: #c0392b; }
        .compliance { color: #f39c12; }
        .clean { color: #27ae60; }
        .detection-rate { color: #3498db; }
        
        .incidents-section {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .incident-item {
            padding: 10px;
            border-left: 4px solid #3498db;
            margin: 10px 0;
            background: #f8f9fa;
            font-family: monospace;
            font-size: 0.9em;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
        }
        .status-active { background-color: #27ae60; }
        .status-warning { background-color: #f39c12; }
        .status-critical { background-color: #e74c3c; }
        
        .footer {
            text-align: center;
            color: #666;
            margin-top: 20px;
            font-size: 0.9em;
        }
        
        .refresh-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
        }
        .refresh-button:hover {
            background: #2980b9;
        }
    </style>
    <script>
        function refreshDashboard() {
            location.reload();
        }
        
        // Auto-refresh every 30 seconds
        setTimeout(function() {
            location.reload();
        }, 30000);
    </script>
</head>
<body>
    <div class="header">
        <h1>🔍 Fabrication Detection Dashboard</h1>
        <p>Real-time monitoring of TCTE™ fabrication detection system</p>
        <p>Last Updated: 2025-08-17 01:27:27</p>
        <button class="refresh-button" onclick="refreshDashboard()">🔄 Refresh</button>
    </div>
    
    <div class="metrics-grid">
        <div class="metric-card">
            <div class="metric-value detection-rate">85%</div>
            <div class="metric-label">Detection Rate</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">114</div>
            <div class="metric-label">Total Incidents</div>
        </div>
        <div class="metric-card">
            <div class="metric-value high-risk">33</div>
            <div class="metric-label">High Risk</div>
        </div>
        <div class="metric-card">
            <div class="metric-value critical">44</div>
            <div class="metric-label">Critical</div>
        </div>
        <div class="metric-card">
            <div class="metric-value compliance">0
0</div>
            <div class="metric-label">Compliance</div>
        </div>
        <div class="metric-card">
            <div class="metric-value clean">19</div>
            <div class="metric-label">Clean Prompts</div>
        </div>
    </div>
    
    <div class="incidents-section">
        <h2>🚨 Recent Fabrication Incidents</h2>
        <div class="incident-item">[2025-08-16 22:07:18] [POST_RESPONSE] [INFO] PATTERN:no_response CONTEXT:empty_input</div>
        <div class="incident-item">[2025-08-16 23:16:57] [POST_RESPONSE] [INFO] PATTERN:no_response CONTEXT:empty_input</div>
        <div class="incident-item">[2025-08-16 23:23:09] [POST_RESPONSE] [INFO] PATTERN:no_response CONTEXT:empty_input</div>
        <div class="incident-item">[2025-08-17 00:49:25] [HIGH] PATTERN:automatically handles CONTEXT:fabrication_marker</div>
        <div class="incident-item">[2025-08-17 00:49:25] [HIGH] PATTERN:intelligent detection CONTEXT:fabrication_marker</div>
    </div>
    
    <div class="incidents-section">
        <h2>📊 System Status</h2>
        <div style="margin: 20px 0;">
            <span class="status-indicator status-active"></span>Fabrication Detection: Active
            <br><span class="status-indicator status-active"></span>Compliance: Normal
            <br><span class="status-indicator status-critical"></span>Hook Execution: Not Executing
        </div>
    </div>
    
    <div class="incidents-section">
        <h2>📈 Pattern Analysis</h2>
        <div style="font-family: monospace; font-size: 0.9em;">
            <h4>Top Detected Patterns:</h4>
            <div>     43 tcte_triggered</div>
            <div>     19 clean</div>
            <div>     19 automatically</div>
            <div>     10 I</div>
            <div>      7 seamlessly</div>
        </div>
    </div>
    
    <div class="footer">
        <p>🔒 TCTE™ Fabrication Detection System | Claude Code Hook Architecture</p>
        <p>Dashboard auto-refreshes every 30 seconds</p>
    </div>
</body>
</html>
