#!/bin/bash
# global_claude_enforcer.sh
# UserPromptSubmit hook that enforces global CLAUDE.md adherence
# Updated: 2025-08-01 - Made hierarchy-aware with compliance tracking
# Created: 2025-07-30 for global enforcement fix

# Get current date for reminders
CURRENT_DATE=$(date +%Y-%m-%d)
CURRENT_YEAR_MONTH=$(date +%Y-%m)

# Source hierarchy utils and compliance tracker
HOOK_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$HOOK_DIR/lib/hierarchy-utils.sh"
source "$HOOK_DIR/lib/compliance-tracker.sh"

# Source logging if available
if [ -f ~/.claude/hooks/lib/logging.sh ]; then
    source ~/.claude/hooks/lib/logging.sh
else
    log_event() { echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> ~/.claude/hooks/logs/hooks.jsonl; }
fi

# Get working directory and find CLAUDE.md hierarchy
WORKING_DIR="${CLAUDE_WORKING_DIRECTORY:-$(pwd)}"
HIERARCHY=($(find_claude_hierarchy "$WORKING_DIR"))
HIERARCHY_COUNT=${#HIERARCHY[@]}

# Log hierarchy detection
log_hierarchy_detection "$WORKING_DIR" "$HIERARCHY_COUNT"
log_event "GLOBAL_ENFORCEMENT: Found $HIERARCHY_COUNT CLAUDE.md files in hierarchy"

# Check compliance and capture result
COMPLIANCE_RESULT=$(check_hierarchy_compliance "$WORKING_DIR" "prompt")
if [[ "$COMPLIANCE_RESULT" == "true" ]]; then
    log_event "GLOBAL_ENFORCEMENT: Compliance check PASSED for $WORKING_DIR"
    # Log success event for accurate tracking
    log_compliance_event "success" "Global" '{"reason":"hierarchy_compliant","hook":"global_claude_enforcer"}'
else
    log_event "GLOBAL_ENFORCEMENT: Compliance check FAILED for $WORKING_DIR"
    # Violation already logged by check_hierarchy_compliance
fi

# Build enforcement message with hierarchy awareness
# Redirect to stderr to prevent UI display while maintaining enforcement
echo "⚠️ MANDATORY HIERARCHY-AWARE ENFORCEMENT:" >&2
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" >&2
echo "📁 Working Directory: $WORKING_DIR" >&2
echo "📚 CLAUDE.md Hierarchy ($HIERARCHY_COUNT levels detected):" >&2

# Show all CLAUDE.md files in hierarchy (most specific first)
for i in "${!HIERARCHY[@]}"; do
    level=$(get_hierarchy_level "${HIERARCHY[$i]}")
    echo "   $(($i + 1)). [$level] ${HIERARCHY[$i]}" >&2
done

echo "" >&2
echo "🔝 GLOBAL FIRST PRINCIPLE: /home/<USER>/.claude/CLAUDE.md takes absolute precedence" >&2
echo "📋 You MUST follow ALL $(($HIERARCHY_COUNT)) CLAUDE.md files, with global overriding others" >&2
echo "" >&2
echo "✅ START your response by displaying the 5 CRITICAL OPERATING PRINCIPLES from global CLAUDE.md" >&2
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" >&2
echo "" >&2
echo "📅 CURRENT SYSTEM TIME: $(date) - ALWAYS use 'date' command!" >&2
echo "📅 Today (via system): $CURRENT_DATE | Year-Month: $CURRENT_YEAR_MONTH" >&2
echo "⚠️ NEVER use stale <env> date for files - use dynamic \$(date) command!" >&2
echo "✅ For timestamps: \$(date +%Y%m%d-%H%M%S) gives current time!" >&2

# Always allow the prompt to continue
exit 0