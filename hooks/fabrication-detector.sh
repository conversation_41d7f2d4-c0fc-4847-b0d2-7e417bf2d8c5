#!/bin/bash
# Fabrication Detection Hook - PERVASIVE Implementation
# Type: UserPromptSubmit (Proactive)
# Purpose: Detect fabrication patterns in EVERY action/response
# Created: $(date '+%Y-%m-%d %H:%M:%S')

# Source utilities
source ~/.claude/hooks/lib/date-utils.sh 2>/dev/null || {
    # Fallback date function if utils not available
    get_timestamp() { date '+%Y-%m-%d %H:%M:%S'; }
}

# Fabrication pattern database (based on TCTE™ analysis)
FABRICATION_PATTERNS=(
    "automatically handles"
    "automatically manages"
    "automatically detects"
    "seamlessly integrates"
    "seamlessly connects"
    "seamlessly works"
    "intelligent system"
    "intelligent detection"
    "smart detection"
    "smart system"
    "advanced AI"
    "cutting-edge"
    "state-of-the-art"
    "machine learning algorithm"
    "AI-powered"
    "neural network"
    "deep learning"
    "sophisticated algorithm"
    "enterprise-grade"
    "production-ready"
    "industry-standard"
    "world-class"
    "revolutionary"
    "breakthrough"
    "next-generation"
)

# Capability claim patterns that need verification
CAPABILITY_PATTERNS=(
    "I can automatically"
    "This will automatically"
    "It automatically"
    "The system will"
    "This integrates with"
    "This connects to"
    "I have access to"
    "I can connect to"
    "I'm able to"
    "This enables"
    "This provides"
    "Built-in support for"
    "Native integration"
    "Direct connection"
)

# Log file
LOG_FILE="$HOME/.claude/fabrication-prevention/logs/fabrication-incidents.log"
touch "$LOG_FILE"

# Function to log incidents
log_incident() {
    local level="$1"
    local pattern="$2"
    local context="$3"
    echo "[$(get_timestamp)] [$level] PATTERN:$pattern CONTEXT:$context" >> "$LOG_FILE"
}

# Function to check for fabrication patterns
check_fabrication_patterns() {
    local text="$1"
    local detected_patterns=()
    
    # Check fabrication patterns
    for pattern in "${FABRICATION_PATTERNS[@]}"; do
        if echo "$text" | grep -qi "$pattern"; then
            detected_patterns+=("FABRICATION:$pattern")
            log_incident "HIGH" "$pattern" "fabrication_marker"
        fi
    done
    
    # Check capability patterns  
    for pattern in "${CAPABILITY_PATTERNS[@]}"; do
        if echo "$text" | grep -qi "$pattern"; then
            detected_patterns+=("CAPABILITY:$pattern")
            log_incident "MEDIUM" "$pattern" "capability_claim"
        fi
    done
    
    if [ ${#detected_patterns[@]} -gt 0 ]; then
        echo "⚠️ FABRICATION RISK DETECTED:"
        printf '   %s\n' "${detected_patterns[@]}"
        echo ""
        echo "🔍 TRIGGERING TCTE™ VERIFICATION..."
        
        # Return non-zero to indicate fabrication detected
        return 1
    fi
    
    return 0
}

# Function to trigger TCTE™ verification
trigger_tcte_verification() {
    local detected_text="$1"
    
    echo "📋 TCTE™ VERIFICATION REQUIRED:"
    echo "   Tier 1: Check official documentation"
    echo "   Tier 2: Direct testing if possible"  
    echo "   Tier 3: Community validation"
    echo ""
    echo "💡 RECOMMENDATION: Verify claims before proceeding"
    
    log_incident "CRITICAL" "tcte_triggered" "verification_required"
}

# Main hook execution
main() {
    local prompt="$1"
    
    # If no command line argument, try to read JSON from stdin
    if [ -z "$prompt" ]; then
        if [ -p /dev/stdin ]; then
            # Read JSON input from Claude Code
            local json_input=$(cat)
            if [ -n "$json_input" ]; then
                # Extract prompt from JSON using jq if available
                if command -v jq >/dev/null 2>&1; then
                    prompt=$(echo "$json_input" | jq -r '.prompt // empty')
                else
                    # Fallback: simple grep extraction
                    prompt=$(echo "$json_input" | grep -o '"prompt":"[^"]*"' | sed 's/"prompt":"\(.*\)"/\1/')
                fi
            fi
        fi
    fi
    
    # Skip if no prompt provided
    if [ -z "$prompt" ]; then
        return 0
    fi
    
    # Check for fabrication patterns
    if ! check_fabrication_patterns "$prompt"; then
        trigger_tcte_verification "$prompt"
        
        # Log the full prompt for analysis
        echo "[$(get_timestamp)] FULL_PROMPT: $prompt" >> "$LOG_FILE"
        
        # For testing: return failure code when fabrication detected
        # In production: would return 0 to allow with warning
        echo "⚠️ Proceeding with verification warning. Use '/compliance-expert' for detailed analysis."
        return 1
    fi
    
    # Log clean prompts too (for metrics)
    log_incident "INFO" "clean" "no_fabrication_detected"
    return 0
}

# Execute if run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi