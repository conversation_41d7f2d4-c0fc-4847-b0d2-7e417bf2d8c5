{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["<PERSON><PERSON>(claude debug)", "<PERSON><PERSON>(claude mcp list:*)", "Bash(ls:*)", "Bash(find:*)", "<PERSON><PERSON>(jq:*)", "Bash(grep:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(python3:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "<PERSON><PERSON>(claude help hooks)", "WebFetch(domain:docs.anthropic.com)", "mcp__langfuse__get_performance", "mcp__crawl4ai__scrape_webpage", "WebFetch(domain:github.com)", "WebFetch(domain:raw.githubusercontent.com)", "mcp__sequential-thinking__sequentialthinking", "<PERSON><PERSON>(mkdir:*)", "Bash(bash:*)", "mcp__desktop-commander__search_files", "mcp__desktop-commander__list_processes", "WebFetch(domain:www.figma.com)", "WebFetch(domain:help.figma.com)", "<PERSON><PERSON>(cat:*)", "WebFetch(domain:voltagent.dev)", "<PERSON><PERSON>(claude help)", "mcp__desktop-commander__list_directory", "WebSearch", "<PERSON><PERSON>(test:*)", "mcp__crawl4ai__crawl_website", "WebFetch(domain:claudelog.com)", "mcp__linear__list_teams", "mcp__linear__list_issues", "mcp__linear__list_issue_statuses", "mcp__linear__get_project"], "deny": [], "defaultMode": "acceptEdits"}, "disabledMcpjsonServers": ["browser-mcp", "playwright", "desktop-commander", "crawl4ai", "mermaid", "youtube-transcript", "sequential-thinking", "langfuse", "n8n-mcp", "wslsnapit", "context7"]}