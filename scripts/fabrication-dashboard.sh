#!/bin/bash
# Fabrication Prevention Dashboard - Simple Observability
# Purpose: Monitor fabrication detection and prevention effectiveness
# Created: $(date '+%Y-%m-%d %H:%M:%S')

# Configuration
LOGS_DIR="$HOME/.claude/fabrication-prevention/logs"
INCIDENTS_LOG="$LOGS_DIR/fabrication-incidents.log"
RECOVERY_LOG="$LOGS_DIR/recovery-attempts.log"
RESPONSIBILITY_LOG="$LOGS_DIR/team-responsibility.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to get current timestamp
get_timestamp() {
    date '+%Y-%m-%d %H:%M:%S'
}

# Function to count log entries by pattern
count_entries() {
    local file="$1"
    local pattern="$2"
    local hours="${3:-24}"
    
    if [[ ! -f "$file" ]]; then
        echo "0"
        return
    fi
    
    # Get entries from last N hours
    local cutoff_time=$(date -d "$hours hours ago" '+%Y-%m-%d %H:%M:%S')
    awk -v cutoff="$cutoff_time" -v pattern="$pattern" '
        $1 " " $2 >= cutoff && $0 ~ pattern { count++ }
        END { print count+0 }
    ' "$file"
}

# Function to get latest entries
get_latest_entries() {
    local file="$1"
    local count="${2:-5}"
    
    if [[ ! -f "$file" ]]; then
        echo "No entries found"
        return
    fi
    
    tail -n "$count" "$file" | while read -r line; do
        echo "   $line"
    done
}

# Main dashboard function
generate_dashboard() {
    clear
    echo -e "${BLUE}===============================================${NC}"
    echo -e "${BLUE}    FABRICATION PREVENTION DASHBOARD${NC}"
    echo -e "${BLUE}===============================================${NC}"
    echo -e "Generated: $(get_timestamp)"
    echo -e "Logs Location: $LOGS_DIR"
    echo ""
    
    # System Status
    echo -e "${GREEN}📊 SYSTEM STATUS (Last 24 Hours)${NC}"
    echo "----------------------------------------"
    
    # Count different types of incidents
    local high_risk=$(count_entries "$INCIDENTS_LOG" "HIGH" 24)
    local medium_risk=$(count_entries "$INCIDENTS_LOG" "MEDIUM" 24)
    local critical_risk=$(count_entries "$INCIDENTS_LOG" "CRITICAL" 24)
    local total_incidents=$((high_risk + medium_risk + critical_risk))
    
    # Recovery attempts
    local recovery_attempts=$(count_entries "$RECOVERY_LOG" "RECOVERY" 24)
    local successful_recoveries=$(count_entries "$RECOVERY_LOG" "SUCCESS" 24)
    local escalations=$(count_entries "$RECOVERY_LOG" "ESCALATE" 24)
    
    # Responsibility checks
    local resp_passed=$(count_entries "$RESPONSIBILITY_LOG" "PASSED" 24)
    local resp_warnings=$(count_entries "$RESPONSIBILITY_LOG" "WARNING" 24)
    local resp_failed=$(count_entries "$RESPONSIBILITY_LOG" "FAILED" 24)
    local total_resp_checks=$((resp_passed + resp_warnings + resp_failed))
    
    # Display metrics
    echo -e "🚨 Fabrication Incidents: ${RED}$total_incidents${NC}"
    echo -e "   └─ Critical: ${RED}$critical_risk${NC}, High: ${YELLOW}$high_risk${NC}, Medium: ${YELLOW}$medium_risk${NC}"
    echo ""
    
    echo -e "🔄 Recovery Operations: ${BLUE}$recovery_attempts${NC}"
    echo -e "   ├─ Successful: ${GREEN}$successful_recoveries${NC}"
    echo -e "   └─ Escalated: ${RED}$escalations${NC}"
    
    if [[ $recovery_attempts -gt 0 ]]; then
        local success_rate=$((successful_recoveries * 100 / recovery_attempts))
        echo -e "   └─ Success Rate: ${GREEN}$success_rate%${NC}"
    fi
    echo ""
    
    echo -e "👥 Team Responsibility: ${BLUE}$total_resp_checks${NC} checks"
    echo -e "   ├─ Passed: ${GREEN}$resp_passed${NC}"
    echo -e "   ├─ Warnings: ${YELLOW}$resp_warnings${NC}"
    echo -e "   └─ Failed: ${RED}$resp_failed${NC}"
    
    if [[ $total_resp_checks -gt 0 ]]; then
        local resp_success_rate=$((resp_passed * 100 / total_resp_checks))
        echo -e "   └─ Success Rate: ${GREEN}$resp_success_rate%${NC}"
    fi
    echo ""
    
    # System Health Assessment
    echo -e "${GREEN}🏥 SYSTEM HEALTH ASSESSMENT${NC}"
    echo "----------------------------------------"
    
    if [[ $critical_risk -eq 0 && $escalations -eq 0 ]]; then
        echo -e "Status: ${GREEN}HEALTHY${NC} ✅"
        echo "└─ No critical incidents or escalations"
    elif [[ $critical_risk -gt 0 || $escalations -gt 0 ]]; then
        echo -e "Status: ${RED}ATTENTION REQUIRED${NC} ⚠️"
        echo "└─ Critical incidents or escalations detected"
    else
        echo -e "Status: ${YELLOW}MONITORING${NC} 👀"
        echo "└─ Normal operation with some warnings"
    fi
    echo ""
    
    # Recent Activity
    if [[ $total_incidents -gt 0 ]]; then
        echo -e "${YELLOW}🔍 RECENT FABRICATION INCIDENTS${NC}"
        echo "----------------------------------------"
        get_latest_entries "$INCIDENTS_LOG" 5
        echo ""
    fi
    
    if [[ $escalations -gt 0 ]]; then
        echo -e "${RED}🚨 RECENT ESCALATIONS${NC}"
        echo "----------------------------------------"
        grep "ESCALATE" "$RECOVERY_LOG" | tail -3 | while read -r line; do
            echo "   $line"
        done
        echo ""
    fi
    
    # Quick Actions
    echo -e "${BLUE}⚡ QUICK ACTIONS${NC}"
    echo "----------------------------------------"
    echo "View live incidents: tail -f $INCIDENTS_LOG"
    echo "Test detection:     ~/.claude/hooks/fabrication-detector.sh 'test message'"
    echo "Check responsibility: ~/.claude/hooks/valued-team-member.sh check 'test action'"
    echo "Run recovery test:  ~/.claude/hooks/fabrication-recovery-loop.sh 'test_claim'"
    echo ""
    
    # Footer
    echo -e "${BLUE}===============================================${NC}"
    echo -e "Dashboard auto-refreshes every 30 seconds"
    echo -e "Press Ctrl+C to exit"
    echo -e "${BLUE}===============================================${NC}"
}

# Function to continuously monitor
monitor_continuously() {
    while true; do
        generate_dashboard
        sleep 30
    done
}

# Function to generate summary report
generate_summary_report() {
    local hours="${1:-24}"
    
    echo "📋 FABRICATION PREVENTION SUMMARY REPORT"
    echo "Time Period: Last $hours hours"
    echo "Generated: $(get_timestamp)"
    echo ""
    
    # Basic statistics
    local incidents=$(count_entries "$INCIDENTS_LOG" "" "$hours")
    local recoveries=$(count_entries "$RECOVERY_LOG" "SUCCESS" "$hours")
    local resp_checks=$(count_entries "$RESPONSIBILITY_LOG" "" "$hours")
    
    echo "📊 SUMMARY STATISTICS:"
    echo "   Total fabrication checks: $incidents"
    echo "   Successful recoveries: $recoveries"
    echo "   Responsibility validations: $resp_checks"
    echo ""
    
    # Top patterns
    echo "🔍 TOP FABRICATION PATTERNS:"
    if [[ -f "$INCIDENTS_LOG" ]]; then
        grep "PATTERN:" "$INCIDENTS_LOG" | tail -50 | cut -d':' -f3 | sort | uniq -c | sort -nr | head -5 | while read -r count pattern; do
            echo "   $count × $pattern"
        done
    fi
    echo ""
    
    # Recommendations
    echo "💡 RECOMMENDATIONS:"
    local high_risk=$(count_entries "$INCIDENTS_LOG" "HIGH" "$hours")
    if [[ $high_risk -gt 5 ]]; then
        echo "   ⚠️ High number of fabrication incidents - review detection patterns"
    fi
    
    local escalations=$(count_entries "$RECOVERY_LOG" "ESCALATE" "$hours")
    if [[ $escalations -gt 0 ]]; then
        echo "   🚨 Recovery escalations detected - manual review needed"
    fi
    
    if [[ $incidents -eq 0 ]]; then
        echo "   ✅ No fabrication incidents - system operating normally"
    fi
}

# Main function
main() {
    local command="$1"
    
    case "$command" in
        "monitor"|"")
            monitor_continuously
            ;;
        "once")
            generate_dashboard
            ;;
        "report")
            generate_summary_report "${2:-24}"
            ;;
        "test")
            echo "🧪 Testing fabrication prevention components..."
            echo ""
            
            # Test detection
            echo "Testing fabrication detector..."
            ~/.claude/hooks/fabrication-detector.sh "This automatically handles everything seamlessly"
            echo ""
            
            # Test responsibility
            echo "Testing team responsibility..."
            ~/.claude/hooks/valued-team-member.sh quick "I can automatically solve any problem"
            echo ""
            
            echo "✅ Test completed - check logs for results"
            ;;
        *)
            echo "Usage: $0 {monitor|once|report|test} [hours]"
            echo ""
            echo "Commands:"
            echo "  monitor  - Continuous monitoring (default)"
            echo "  once     - Single dashboard view"
            echo "  report   - Generate summary report"
            echo "  test     - Test system components"
            echo ""
            echo "Examples:"
            echo "  $0 monitor"
            echo "  $0 report 48"
            echo "  $0 test"
            return 1
            ;;
    esac
}

# Execute if run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi